{"nodes": [{"id": "d6d099c6cf9074a9", "type": "text", "text": "[ALGOL 58](file:///Users/<USER>/Literature/CS/Languages/algol58_1958.pdf) ([[algol_58|note]])\n1958", "x": -240, "y": -480, "width": 240, "height": 80, "color": "5"}, {"id": "a9dbba8479ad4b1c", "type": "text", "text": "LISP\n1958", "x": -240, "y": -400, "width": 240, "height": 80, "color": "6"}, {"id": "44360019663262fa", "type": "text", "text": "[LISP 1.5](file:///Users/<USER>/Literature/CS/Languages/lisp1.5_1962(2ed_1985).pdf)\n1962", "x": 1200, "y": -400, "width": 240, "height": 80, "color": "6"}, {"id": "d70d8e78ceb3e694", "type": "text", "text": "[ALGOL 60](file:///Users/<USER>/Literature/CS/Languages/algol60_1960.pdf) ([[algol_60|note]])\n1960", "x": 480, "y": -480, "width": 240, "height": 80, "color": "5"}, {"id": "c0ebef496bbf60ff", "type": "text", "text": "[ALGOL 68](file:///Users/<USER>/Literature/CS/Languages/algol60_1960.pdf)\nDec 1968", "x": 3360, "y": -480, "width": 240, "height": 80, "color": "5"}, {"id": "94776504e026acbe", "type": "text", "text": "[Common Lisp](file:///Users/<USER>/Literature/CS/Languages/common_lisp_1984.pdf)\n1984", "x": 9120, "y": -400, "width": 240, "height": 80, "color": "6"}, {"id": "b03b6f32f4b029b9", "type": "text", "text": "Common Lisp ANSI\nDec 1994", "x": 12720, "y": -400, "width": 240, "height": 80, "color": "6"}, {"id": "6f595d87160f13b0", "type": "text", "text": "[LISP 1](file:///Users/<USER>/Literature/CS/Languages/lisp_1960.pdf)\n1959", "x": 120, "y": -400, "width": 240, "height": 80, "color": "6"}, {"id": "d1299e6c3399a8bb", "type": "text", "text": "Visual Basic 2015\n2015", "x": 20280, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "ff635debb93e8f49", "type": "text", "text": "Visual Basic 2017\n2017", "x": 21000, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "c651e5700bbafbfc", "type": "text", "text": "Visual Basic 2019\n2019", "x": 21720, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "9861d02642f9fcd5", "type": "text", "text": "Visual Basic 2012\n2012", "x": 19200, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "d366bf1c7b6e4ecc", "type": "text", "text": "Visual Basic 2013\n2013", "x": 19560, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "067d433ea664c147", "type": "text", "text": "[Visual Basic 1.0](file:///Users/<USER>/Literature/CS/Languages/mv_basic(ref)_1991.pdf)\n1991", "x": 11640, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "45047f152878c2f1", "type": "text", "text": "Visaul Basic 6.0\nJun 1998", "x": 14160, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "0e3495572794d8c9", "type": "text", "text": "APL96\n1996", "x": 13440, "y": -160, "width": 240, "height": 80, "color": "3"}, {"id": "f4dbd9f41c5a4e3f", "type": "text", "text": "Visual Basic .NET\n2002", "x": 15600, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "e8b0f0a27d68ae17", "type": "text", "text": "Visual Basic .NET 2003\n2003", "x": 15960, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "87b734ef5cf2cfe1", "type": "text", "text": "Visual Basic 2005\n2005", "x": 16680, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "ba7f0fa40442eb0c", "type": "text", "text": "Visual Basic 9.0\n2008", "x": 17760, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "c8393c3eaa55fffd", "type": "text", "text": "Visual Basic 2010\n2010", "x": 18480, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "981a0fe131abc6c3", "type": "text", "text": "APL 2\nAug 1984", "x": 9120, "y": -160, "width": 240, "height": 80, "color": "3"}, {"id": "96bb9f881924de3f", "type": "text", "text": "MS Basic 2.0\nJul 1975", "x": 5880, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "0b3712f3368ea93c", "type": "text", "text": "PL/1 ANS\n1976", "x": 6240, "y": 400, "width": 240, "height": 80, "color": "#d14f2e"}, {"id": "8b4c1fe1ab07f152", "type": "text", "text": "[Simula67](file:///Users/<USER>/Literature/CS/Languages/simula67_1970pdf.pdf)\n1967", "x": 3000, "y": 480, "width": 240, "height": 80, "color": "#719d20"}, {"id": "c2dce253fcac2fbd", "type": "text", "text": "[ALGOL W](file:///Users/<USER>/Literature/CS/Languages/algolw_1972.pdf)\n1966", "x": 2640, "y": 720, "width": 240, "height": 80, "color": "1"}, {"id": "6791f4000efd2b0f", "type": "text", "text": "[ISWIM](file:)\n1966", "x": 2640, "y": 800, "width": 240, "height": 80, "color": "#5f8bf2"}, {"id": "b4e13a9149cf0bfb", "type": "text", "text": "[SNOBOL 4](file:///Users/<USER>/Literature/CS/Languages/snobol4_1968.pdf)\n1968", "x": 3360, "y": 80, "width": 240, "height": 80, "color": "4"}, {"id": "715064fdd0c7c550", "type": "text", "text": "[BCPL](file:///Users/<USER>/Literature/CS/Languages/bcpl_1967.pdf)\nJul 1967", "x": 3000, "y": 200, "width": 240, "height": 80, "color": "#b78a8a"}, {"id": "0b962acecaabf739", "type": "text", "text": "[B](file:///Users/<USER>/Literature/CS/Languages/b_1972.pdf)\n1969", "x": 3720, "y": 200, "width": 240, "height": 80, "color": "#b78a8a"}, {"id": "0d75bff66cf846be", "type": "text", "text": "SNOBOL 2\nApr 1964", "x": 1920, "y": 80, "width": 240, "height": 80, "color": "4"}, {"id": "adc16d8d4aa31f0d", "type": "text", "text": "SNOBOL 3\n1965", "x": 2280, "y": 80, "width": 240, "height": 80, "color": "4"}, {"id": "de1b0df301cc15f8", "type": "text", "text": "[BASIC](file:///Users/<USER>/Literature/CS/Languages/basic_1964.pdf)\nMay 1964", "x": 1920, "y": 320, "width": 240, "height": 80, "color": "#ec88d7"}, {"id": "f147f371c149bf7d", "type": "text", "text": "[PL/I](file:///Users/<USER>/Literature/CS/Languages/pl1_1965.pdf)\n1964", "x": 1920, "y": 400, "width": 240, "height": 80, "color": "#d14f2e"}, {"id": "47ed4df1a89a7e41", "type": "text", "text": "[Simula I](file:///Users/<USER>/Literature/CS/Languages/simula_1962(5ed_1967).pdf)\n1964", "x": 1920, "y": 480, "width": 240, "height": 80, "color": "#719d20"}, {"id": "34708de806194c2d", "type": "text", "text": "[CPL](file:///Users/<USER>/Literature/CS/Languages/cpl_1966.pdf)\n1963", "x": 1560, "y": 200, "width": 240, "height": 80, "color": "#b78a8a"}, {"id": "c2135b11ad0c621a", "type": "text", "text": "[SNOBOL](file:///Users/<USER>/Literature/CS/Languages/snobol_1962.pdf)\n1962", "x": 1200, "y": 80, "width": 240, "height": 80, "color": "4"}, {"id": "34d8fd541ccb3255", "type": "text", "text": "[APL](file:///Users/<USER>/Literature/CS/Languages/apl_1962(4ed_1967).pdf)\n1960", "x": 480, "y": -160, "width": 240, "height": 80, "color": "3"}, {"id": "2b85a5912c6b5890", "type": "text", "text": "[COBOL](file:///Users/<USER>/Literature/CS/Languages/cobol_1960.pdf)\n1959", "x": 120, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "8868b97247b5867b", "type": "text", "text": "COBOL 61\n1961", "x": 840, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "dfc2993b5696074e", "type": "text", "text": "COBOL 61 Extended\n1962", "x": 1200, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "3c3d07ef80764f97", "type": "text", "text": "COBOL 65\n1965", "x": 2280, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "0aa238523fd00501", "type": "text", "text": "COBOL 68\n1968", "x": 3360, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "0dd170c33f34956f", "type": "text", "text": "COBOL 74\n1974", "x": 5520, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "6ae2fbeb4ae24416", "type": "text", "text": "COBOL 85", "x": 9480, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "2aca7ca44f752cf0", "type": "text", "text": "COBOL 2002", "x": 15600, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "89d4394cf3020b85", "type": "text", "text": "COBOL 2014\n2014", "x": 19920, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "9c67e42943c1073d", "type": "text", "text": "COBOL 2023\n2023", "x": 23160, "y": -280, "width": 240, "height": 80, "color": "1"}, {"id": "7ebb0d6d8492645a", "type": "text", "text": "[COMIT](file:///Users/<USER>/Literature/CS/Languages/comit_1958.pdf)\n1957", "x": -600, "y": -680, "width": 240, "height": 80, "color": "#e01f99"}, {"id": "fd4ac995fcf25a84", "type": "text", "text": "[Flow-Matic](file:///Users/<USER>/Literature/CS/Languages/flow-matic_1958.pdf)\n1955", "x": -1180, "y": -1400, "width": 180, "height": 80, "color": "#9536a3"}, {"id": "25dac6dd45c12bef", "type": "text", "text": "FORTRAN IV\n1962", "x": 1200, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "92c6dcc6d8abda67", "type": "text", "text": "FORTRAN III\nend-1958", "x": -240, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "3c914e72ce82d1d0", "type": "text", "text": "[FORTRAN II](file:///Users/<USER>/Literature/CS/Languages/fortranii_1958.pdf)\n1957", "x": -600, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "753762f9de2b747c", "type": "text", "text": "FORTRAN\nNov 1954", "x": -1680, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "1938ac6a7704881c", "type": "text", "text": "[FORTRAN I](file:///Users/<USER>/Literature/CS/Languages/fortran_1956.pdf)\nOct 1956", "x": -960, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "b2bde5d46a64d9c2", "type": "text", "text": "Fortran 2018\n2018", "x": 21360, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "4ec63e2651c8f05e", "type": "text", "text": "Fortran 2023\n2023", "x": 23160, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "394cd599c5d0fa03", "type": "text", "text": "[Fortran 90](file:///Users/<USER>/Literature/CS/Languages/fortran90_1991.pdf)\n1991", "x": 11640, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "65141b302af3a257", "type": "text", "text": "Fortran 95\nDec 1997", "x": 13800, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "f12a1d1c24799b85", "type": "text", "text": "Fortran 2003\n2004", "x": 15960, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "945e157b65280998", "type": "text", "text": "Fortran 2008\n2010", "x": 18480, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "9fcb62c270aac6f4", "type": "text", "text": "[FORTRAN 77](file:///Users/<USER>/Literature/CS/Languages/fortran77_1978.pdf)\nApr 1978", "x": 6960, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "197e395b9a44fbd2", "type": "text", "text": "[FORTRAN 66](file:///Users/<USER>/Literature/CS/Languages/fortran66_1966.pdf)\n1966", "x": 2640, "y": -1040, "width": 240, "height": 80, "color": "2"}, {"id": "a88aeaf74090ef52", "type": "text", "text": "[Ada](file:///Users/<USER>/Literature/CS/Languages/ada_1980.pdf)\n1979", "x": 7320, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "419c93edd0eb2437", "type": "text", "text": "Ada 83 ANSI\nJan 1983", "x": 8760, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "8463660da76a9feb", "type": "text", "text": "C with classes\n1980", "x": 7680, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "2cf8964ff0fe18ee", "type": "text", "text": "[C++](file:///Users/<USER>/Literature/CS/Languages/cpp_1984.pdf)\nJul 1983", "x": 8760, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "527e37f058438176", "type": "text", "text": "[KRC](file:///Users/<USER>/Literature/CS/Languages/krc_1982.pdf)\n1981", "x": 8040, "y": 3520, "width": 240, "height": 80, "color": "1"}, {"id": "d2259e6be4fdedc9", "type": "text", "text": "[B](file:///Users/<USER>/Literature/CS/Languages/b_1981.pdf)\n1981", "x": 8040, "y": 3600, "width": 240, "height": 80, "color": "#ab2b31"}, {"id": "4aaa9ccc6b68de52", "type": "text", "text": "Prolog II\nOct 1982", "x": 8400, "y": 1840, "width": 240, "height": 80, "color": "#92c256"}, {"id": "48e446b49758b138", "type": "text", "text": "Prolog III\n1984", "x": 9120, "y": 1840, "width": 240, "height": 80, "color": "#92c256"}, {"id": "202776b8ca29173a", "type": "text", "text": "[Standard ML](file:///Users/<USER>/Literature/CS/Languages/standardml_(corelang)_1985.pdf)\n1984", "x": 9120, "y": 1960, "width": 240, "height": 80, "color": "#8d2020"}, {"id": "5635c7fc9f4e5b10", "type": "text", "text": "Scheme 84\n1984", "x": 9120, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "c0713e100b988837", "type": "text", "text": "[nawk](http://man.he.net/man1/nawk)\n1985", "x": 9480, "y": 2880, "width": 240, "height": 80, "color": "#c85b60"}, {"id": "93f593092c9707b4", "type": "text", "text": "Smalltalk-78\n1978", "x": 6960, "y": 1160, "width": 240, "height": 80, "color": "#00706e"}, {"id": "bfd1b250699404d5", "type": "text", "text": "[C (K&R)](file:///Users/<USER>/Literature/CS/Languages/c_1978.pdf)\n1978", "x": 6960, "y": 1640, "width": 240, "height": 80, "color": "#28b525"}, {"id": "f995802ac67a2293", "type": "text", "text": "Scheme MIT\n1978", "x": 6960, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "666130658c516db9", "type": "text", "text": "[awk](file:///Users/<USER>/Literature/CS/Languages/awk_1988.pdf)\n1978", "x": 6960, "y": 2880, "width": 240, "height": 80, "color": "#c85b60"}, {"id": "a4941f76b6a4712b", "type": "text", "text": "[Modula 2](https://www.modula2.org/reference/)\n1979", "x": 7320, "y": 2440, "width": 240, "height": 80, "color": "#1d6d35"}, {"id": "7d753ded1a1f1d46", "type": "text", "text": "[Smalltalk-80](file:///Users/<USER>/Literature/CS/Languages/smalltalk80_1983.pdf)\n1980", "x": 7680, "y": 1160, "width": 240, "height": 80, "color": "#00706e"}, {"id": "5c4eb58efba1ee96", "type": "text", "text": "Postscript\n1982", "x": 8400, "y": 3720, "width": 240, "height": 80, "color": "3"}, {"id": "e39931030afc50e0", "type": "text", "text": "[<PERSON>](file:///Users/<USER>/Literature/CS/Languages/miranda_1985.pdf)\n1982", "x": 8400, "y": 3800, "width": 240, "height": 80, "color": "5"}, {"id": "88b9dcaffabe4cad", "type": "text", "text": "[CLU](file:///Users/<USER>/Literature/CS/Languages/clu_1979.pdf)\n1975", "x": 5880, "y": 2280, "width": 240, "height": 80, "color": "#f04014"}, {"id": "25e14ceab2b9bd6d", "type": "text", "text": "[Scheme](file:///Users/<USER>/Literature/CS/Languages/scheme_1975.pdf)\n1975", "x": 5880, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "2af38e931b72706b", "type": "text", "text": "[Mesa](file:///Users/<USER>/Literature/CS/Languages/mesa_1979.pdf)\n1977", "x": 6600, "y": 2760, "width": 240, "height": 80, "color": "#555b06"}, {"id": "efa1dcdec2848641", "type": "text", "text": "[<PERSON><PERSON><PERSON>](file:///Users/<USER>/Literature/CS/Languages/modula_1976.pdf)\n1975", "x": 5880, "y": 2440, "width": 240, "height": 80, "color": "#1d6d35"}, {"id": "a18b062351021b4e", "type": "text", "text": "Smalltalk-76\n1976", "x": 6240, "y": 1160, "width": 240, "height": 80, "color": "#00706e"}, {"id": "94cff715bbee4cde", "type": "text", "text": "[ML](file:///Users/<USER>/Literature/CS/Languages/milner-type-polymorphism.pdf)\n1973", "x": 5160, "y": 1960, "width": 240, "height": 80, "color": "#8d2020"}, {"id": "94e7ade9b305e1b2", "type": "text", "text": "C89\n1989", "x": 10920, "y": 1640, "width": 240, "height": 80, "color": "#29b525"}, {"id": "fbf8bae5652ec6af", "type": "text", "text": "C90\n1990", "x": 11280, "y": 1640, "width": 240, "height": 80, "color": "#29b525"}, {"id": "2571b01b629c7026", "type": "text", "text": "SML '90\n1990", "x": 11280, "y": 1960, "width": 240, "height": 80, "color": "#8d2020"}, {"id": "850c224ff2021d17", "type": "text", "text": "Prolog IV\n1997", "x": 13800, "y": 1840, "width": 240, "height": 80, "color": "#92c256"}, {"id": "4c7afb464acb3a79", "type": "text", "text": "SML '97\n1997", "x": 13800, "y": 1960, "width": 240, "height": 80, "color": "#8d2020"}, {"id": "362ef24557d99fc6", "type": "text", "text": "Scheme IEEE\n1990", "x": 11280, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "b3fe3e2eb4e12c01", "type": "text", "text": "Scheme R5RS\n1998", "x": 14160, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "738bbf3eee22378d", "type": "text", "text": "Modula 2 ISO\n1997", "x": 13800, "y": 2440, "width": 240, "height": 80, "color": "#1d6d35"}, {"id": "994ad289ecf416b0", "type": "text", "text": "Ada 95\n1995", "x": 13080, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "1480d0eeeda1a7ec", "type": "text", "text": "C++98\n1998", "x": 14160, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "52ea50cd3f991426", "type": "text", "text": "C++03\n2003", "x": 15960, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "75b8fe9096fc6d4f", "type": "text", "text": "C99\n1999", "x": 14520, "y": 1640, "width": 240, "height": 80, "color": "#29b525"}, {"id": "d86403b3af508d01", "type": "text", "text": "Postscript level 3\nSep 1996", "x": 13440, "y": 3720, "width": 240, "height": 80, "color": "3"}, {"id": "342cd13d3be962b7", "type": "text", "text": "Postcript level 2\n1992", "x": 12000, "y": 3720, "width": 240, "height": 80, "color": "3"}, {"id": "60cd79ceb384da34", "type": "text", "text": "Ada ISO\n1987", "x": 10200, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "4da7574c2ad794de", "type": "text", "text": "[ABC](https://homepages.cwi.nl/~steven/abc/programmers/handbook.html)\n1987", "x": 10200, "y": 3600, "width": 240, "height": 80, "color": "#ab2b31"}, {"id": "87e305d349c19607", "type": "text", "text": "OO Forth\n1987", "x": 10200, "y": 1240, "width": 240, "height": 80, "color": "#7856c8"}, {"id": "afa369bd9303dd4e", "type": "text", "text": "Ada 2022\n2022", "x": 22800, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "925a4b05e753b3ed", "type": "text", "text": "C++17\n2017", "x": 21000, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "1aed8b4b4449ccee", "type": "text", "text": "C17\n2018", "x": 21360, "y": 1640, "width": 240, "height": 80, "color": "#29b525"}, {"id": "10a80a99157f6713", "type": "text", "text": "C++20\n2020", "x": 22080, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "6b7de1bb2f01ec62", "type": "text", "text": "C23\n2024", "x": 23520, "y": 1640, "width": 240, "height": 80, "color": "#29b525"}, {"id": "e1081daf0da03ba9", "type": "text", "text": "C++23\n2023", "x": 23520, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "ac32fb730ac16b9e", "type": "text", "text": "C11\n2011", "x": 18840, "y": 1640, "width": 240, "height": 80, "color": "#29b525"}, {"id": "eee718fa526d7f6b", "type": "text", "text": "Scheme R6RS\nAug 2007", "x": 17400, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "84f5164583901577", "type": "text", "text": "Scheme R7RS\n2013", "x": 19560, "y": 2360, "width": 240, "height": 80, "color": "#c26f0f"}, {"id": "2bd93694b08d6313", "type": "text", "text": "Ada 2005\n2007", "x": 17400, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "22376900bc0111fa", "type": "text", "text": "Ada 2012\n2012", "x": 19200, "y": 3080, "width": 240, "height": 80, "color": "#dd3c8a"}, {"id": "81f5eb2ff5096a84", "type": "text", "text": "C++11\n2011", "x": 18840, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "a6b53cae6b969341", "type": "text", "text": "C++14\n2014", "x": 19920, "y": 3400, "width": 240, "height": 80, "color": "#add02f"}, {"id": "3ac413fc7120245e", "type": "text", "text": "Smalltalk\n1969", "x": 3720, "y": 1160, "width": 240, "height": 80, "color": "#00706e"}, {"id": "a182d7c3d700beae", "type": "text", "text": "[Forth](file:///Users/<USER>/Literature/CS/Languages/forth79_1980.pdf)\n1969", "x": 3720, "y": 1240, "width": 240, "height": 80, "color": "#7856c8"}, {"id": "02f1c60f274abf39", "type": "text", "text": "[PLANNER](file:///Users/<USER>/Literature/CS/Languages/planner_1969.pdf)\n1969", "x": 3720, "y": 1320, "width": 240, "height": 80, "color": "1"}, {"id": "07ea68796b915cb6", "type": "text", "text": "[<PERSON>](file:///Users/<USER>/Literature/CS/Languages/pascal_1974.pdf)\n1970", "x": 4080, "y": 1540, "width": 240, "height": 80, "color": "2"}, {"id": "e2bacfbf90e1d9ac", "type": "text", "text": "C\n1971", "x": 4440, "y": 1640, "width": 240, "height": 80, "color": "#26ec22"}, {"id": "10d372b053c49b92", "type": "text", "text": "Smalltalk-74\n1974", "x": 5520, "y": 1160, "width": 240, "height": 80, "color": "#00706e"}, {"id": "a190bd83eccfac47", "type": "text", "text": "[Smalltalk-72](file:///Users/<USER>/Literature/CS/Languages/smalltalk72_1976.pdf)\n1972", "x": 4800, "y": 1160, "width": 240, "height": 80, "color": "#00706e"}, {"id": "7b053f632b22f37b", "type": "text", "text": "[Prolog](file:///Users/<USER>/Literature/CS/Languages/prolog_1973(fr).pdf)\n1972", "x": 4800, "y": 1840, "width": 240, "height": 80, "color": "#92c256"}, {"id": "b5b9f53f4e419f40", "type": "text", "text": "Haskell 2010\nJul 2010", "x": 18480, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "2350c190fa97a3f4", "type": "text", "text": "Tck/Tk 8.5\nDec 2007", "x": 17400, "y": 5240, "width": 240, "height": 80, "color": "#5233f0"}, {"id": "1927f05d86eeb498", "type": "text", "text": "Self 4.4\nJul 2010", "x": 18480, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "2160fd7562c2f7f7", "type": "text", "text": "Perl 5.12.0\nApr 2010", "x": 18480, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "c6f4c4c8010b6980", "type": "text", "text": "Eiffel 7\n2009", "x": 18120, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "5073e7de6d81bd69", "type": "text", "text": "Eiffel 5\n2005", "x": 16680, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "24790e5e73231b5c", "type": "text", "text": "[Eiffel 6](file:///Users/<USER>/Literature/CS/Languages/eiffel_2006.pdf)\n2006", "x": 17040, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "3c1f4ba53f9a2f71", "type": "text", "text": "Tcl/Tk 8.4\nSep 2002", "x": 15600, "y": 5240, "width": 240, "height": 80, "color": "#5233f0"}, {"id": "d16fb48c73214020", "type": "text", "text": "Self 4.1.6\nSep 2002", "x": 15600, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "fce5b4cf7b45084b", "type": "text", "text": "Perl 5.8.0\nJul 2002", "x": 15600, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "2cc2ec170ebf8907", "type": "text", "text": "Perl 5.8.2\nOct 2003", "x": 15960, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "df0095ab823b02ea", "type": "text", "text": "[Self 4.1](file:///Users/<USER>/Literature/CS/Languages/self_2001.pdf)\nAug 2001", "x": 15240, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "f9ac07081316f7b6", "type": "text", "text": "Tcl/Tk 8.3\nOct 2001", "x": 15240, "y": 5240, "width": 240, "height": 80, "color": "#5233f0"}, {"id": "0c09a058cf468575", "type": "text", "text": "Haskell 98\nFeb 1999", "x": 14520, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "02638b7db2030656", "type": "text", "text": "[Tcl/Tk 8.1](https://web.archive.org/web/19990203032112/http://scriptics.com/man/tcl8.1/contents.htm)\nApr 1999", "x": 14480, "y": 5240, "width": 160, "height": 80, "color": "#5233f0"}, {"id": "f5fcd082502ca8c3", "type": "text", "text": "Haskell 1.4\nApr 1997", "x": 13800, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "4066a5702794d7fa", "type": "text", "text": "Perl 5.005_50\nJul 1998", "x": 14160, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "156d467a3d1592e3", "type": "text", "text": "[Perl 5.000](file:///Users/<USER>/Literature/CS/Languages/perl_1996.pdf)\nOct 1994", "x": 12720, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "614c3a8d82bf09db", "type": "text", "text": "Haskell 1.3\nMay 1996", "x": 13440, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "54795c07ddd90b14", "type": "text", "text": "Caml 3.1\n1993", "x": 12360, "y": 5120, "width": 240, "height": 80, "color": "4"}, {"id": "28fa63a0fa57c75f", "type": "text", "text": "[Self 4.0](file:///Users/<USER>/Literature/CS/Languages/self_1995.pdf)\nJul 1995", "x": 13080, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "799b759dbb59ca4e", "type": "text", "text": "Eiffel 4\n1995", "x": 13080, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "256ce0855a746621", "type": "text", "text": "Object Rexx\n1997", "x": 13800, "y": 3160, "width": 240, "height": 80, "color": "3"}, {"id": "243367b7f0f47032", "type": "text", "text": "Eiffel\n1986", "x": 9840, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "0db6648d3f3bd36a", "type": "text", "text": "[Objective-C](file:///Users/<USER>/Literature/CS/Languages/objective-c_1991.pdf)\n1983", "x": 8760, "y": 3920, "width": 240, "height": 80, "color": "#ea6e39"}, {"id": "995a16254724d645", "type": "text", "text": "Eiffel 2\n1990", "x": 11280, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "9b106a680f5f8d1b", "type": "text", "text": "Eiffel 3\n1991", "x": 11640, "y": 4520, "width": 240, "height": 80, "color": "#7934b2"}, {"id": "29276080bdaf1df1", "type": "text", "text": "Oberon\n1991", "x": 11640, "y": 4800, "width": 240, "height": 80, "color": "4"}, {"id": "4336571ec0523152", "type": "text", "text": "Perl 4.000\nMar 1991", "x": 11640, "y": 4960, "width": 240, "height": 80, "color": "#53dfdd"}, {"id": "b0c96a36af3dc828", "type": "text", "text": "Haskell 1.1\nApr 1990", "x": 11280, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "1fded19e363d6852", "type": "text", "text": "[Caml 2.6.1](file:///Users/<USER>/Literature/CS/Languages/caml_1990.pdf)\n1991", "x": 11640, "y": 5120, "width": 240, "height": 80, "color": "4"}, {"id": "734ebb83ee303518", "type": "text", "text": "Haskell 1.2\nMar 1992", "x": 12000, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "0b7df5009c3650c2", "type": "text", "text": "perl. 3.000\nOct 1989", "x": 10920, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "e7de6ea303fe22a9", "type": "text", "text": "Tcl\nmid 1988", "x": 10520, "y": 5240, "width": 160, "height": 80, "color": "#5233f0"}, {"id": "d623f2b066846c0b", "type": "text", "text": "Object Rexx\n1987", "x": 10200, "y": 3160, "width": 240, "height": 80, "color": "3"}, {"id": "2969730f9e6c65c9", "type": "text", "text": "[Oberon](file:///Users/<USER>/Literature/CS/Languages/oberon_1990.pdf)\n1987", "x": 10200, "y": 4800, "width": 240, "height": 80, "color": "4"}, {"id": "31e79e3e4f703bdf", "type": "text", "text": "Tcl/Tk 9.0\nSep 2024", "x": 23520, "y": 5240, "width": 240, "height": 80, "color": "#5233f0"}, {"id": "f9483004ffd6b764", "type": "text", "text": "Self 2024.1\n2024", "x": 23520, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "7826b2bc0738ed1c", "type": "text", "text": "Per 5.40.0\n2025", "x": 23880, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "60c1c84a120b1557", "type": "text", "text": "Self 4.5\nJan 2014", "x": 19920, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "3207a448a211fc53", "type": "text", "text": "Perl 5.20.0\nMay 2014", "x": 19920, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "2b2e4795ea7d5523", "type": "text", "text": "Self 2017.1\nMay 2017", "x": 21000, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "08383046ebf8cb87", "type": "text", "text": "Tcl/Tk 8.6\nDec 2012", "x": 19200, "y": 5240, "width": 240, "height": 80, "color": "#5233f0"}, {"id": "2e0ab9bc40028208", "type": "text", "text": "Perl 5.14.0\nMay 2011", "x": 18840, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "932906ddb812db40", "type": "text", "text": "Perl 5.16.0\nMay 2012", "x": 19200, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "bd2ba61f7f82055b", "type": "text", "text": "Perl 5.18.0\nMay 2013", "x": 19560, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "55dfa8fec3d81748", "type": "text", "text": "Rex 1.00\nMay 1979", "x": 7320, "y": 3160, "width": 240, "height": 80, "color": "3"}, {"id": "ce3c1078228a3616", "type": "text", "text": "Rex 2.00\n1980", "x": 7680, "y": 3160, "width": 240, "height": 80, "color": "3"}, {"id": "553557c54a290714", "type": "text", "text": "[Rex 3.00](file:///Users/<USER>/Literature/CS/Languages/rexx_1983.pdf)\n1982", "x": 8400, "y": 3160, "width": 240, "height": 80, "color": "3"}, {"id": "f53d6f6224ce3a3e", "type": "text", "text": "Rexx 3.20\n1984", "x": 9120, "y": 3160, "width": 240, "height": 80, "color": "3"}, {"id": "ba07fc0ecd1be720", "type": "text", "text": "[sh](http://man.cat-v.org/unix-1st/1/sh)\n1971", "x": 4440, "y": 1720, "width": 240, "height": 80, "color": "#da6df8"}, {"id": "172e820c8abce4e1", "type": "text", "text": "[csh](https://docs-archive.freebsd.org/44doc/usd/04.csh/paper.html)\nOct 1978", "x": 6960, "y": 2960, "width": 240, "height": 80, "color": "3"}, {"id": "33cd5f5df1073e3a", "type": "text", "text": "[tcsh](https://web.archive.org/web/20051029095645/http://www.tcsh.org/tcsh.html/top.html)\n1985", "x": 8760, "y": 2960, "width": 240, "height": 80, "color": "3"}, {"id": "b0587dbbe19a64a9", "type": "text", "text": "[Python 3.0.1](https://docs.python.org/3.0/reference/index.html)\nDec 2008", "x": 17760, "y": 5920, "width": 240, "height": 80, "color": "#c63a0c"}, {"id": "9405c4865055a2c0", "type": "text", "text": "[Python 1.5.2](https://docs.python.org/release/1.5.2p2/ref/ref.html)\nApr 1999", "x": 14520, "y": 5920, "width": 240, "height": 80, "color": "#c63a0c"}, {"id": "0bb681d79cac75f6", "type": "text", "text": "[Python 1.2](file:///Users/<USER>/Literature/CS/Languages/python_1995.pdf)\nApr 1995", "x": 13080, "y": 5920, "width": 240, "height": 80, "color": "#c63a0c"}, {"id": "9a3fb98a45c97e6d", "type": "text", "text": "[Python](file:///Users/<USER>/Literature/CS/Languages/python/python.man)\n1991", "x": 11640, "y": 5920, "width": 240, "height": 80, "color": "#c63a0c"}, {"id": "b16637761ff308c5", "type": "text", "text": "[A+](https://web.archive.org/web/20200224202643/http://www.aplusdev.org/APlusRefV2_63.html)\n1992", "x": 12000, "y": 5320, "width": 240, "height": 80, "color": "3"}, {"id": "f2e5202fd6798462", "type": "text", "text": "A\n1988", "x": 10560, "y": 5320, "width": 240, "height": 80, "color": "3"}, {"id": "368e18cd77f9311a", "type": "text", "text": "[Modula 3](https://web.archive.org/web/20050121034545/http://www.research.compaq.com/SRC/m3defn/html/m3.html)\n1988", "x": 10560, "y": 5400, "width": 240, "height": 80, "color": "1"}, {"id": "10185c905868b606", "type": "text", "text": "bash\n1989", "x": 10920, "y": 5600, "width": 240, "height": 80, "color": "5"}, {"id": "37d4e4fa8e222d85", "type": "text", "text": "[Clos](file:///Users/<USER>/Literature/CS/Languages/clos_1986.pdf)\n1989", "x": 10920, "y": 5680, "width": 240, "height": 80, "color": "6"}, {"id": "45ac3b29f492e36d", "type": "text", "text": "Self\n1987", "x": 10200, "y": 4880, "width": 240, "height": 80, "color": "1"}, {"id": "c383751f1d8ec6c7", "type": "text", "text": "perl 1.000\nDec 1987", "x": 10200, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "52f8df88ab92656b", "type": "text", "text": "[Haskell 1.0](file:///Users/<USER>/Literature/CS/Languages/haskell_1990.pdf)\n1987", "x": 10200, "y": 5040, "width": 240, "height": 80, "color": "#965494"}, {"id": "b5ebea958daea3b0", "type": "text", "text": "Caml\n1987", "x": 10200, "y": 5120, "width": 240, "height": 80, "color": "4"}, {"id": "94061c92ba1e27ee", "type": "text", "text": "Perl 5.7.0\nSep 2000", "x": 14980, "y": 4960, "width": 160, "height": 80, "color": "5"}, {"id": "0bfba93dbd23eda1", "type": "text", "text": "Perl 5.6.0\nMar 2000", "x": 14800, "y": 4960, "width": 180, "height": 80, "color": "5"}, {"id": "33467aecc7112eca", "type": "text", "text": "Perl 5.10.0\nDec 2007", "x": 17400, "y": 4960, "width": 240, "height": 80, "color": "5"}, {"id": "5895538a36bd7663", "type": "text", "text": "Sharp APL\n1979", "x": 7320, "y": 3280, "width": 240, "height": 80, "color": "4"}, {"id": "d0b362ec28377aeb", "type": "text", "text": "[J](https://www.jsoftware.com/help/dictionary/title.htm)\n1990", "x": 11280, "y": 3280, "width": 240, "height": 80, "color": "4"}, {"id": "48dbb9ed81fec653", "type": "text", "text": "K\n1996", "x": 13440, "y": 3280, "width": 240, "height": 80, "color": "4"}, {"id": "7ab7b0d79a8e7233", "type": "text", "text": "Java SE 24\nMar 2025", "x": 23880, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "299fe65c31b09983", "type": "text", "text": "Java SE 10\nMar 2018", "x": 21360, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "5bffc1471db5acc5", "type": "text", "text": "[Javascript 1.5](https://web.archive.org/web/20040814085948/http://devedge.netscape.com/library/manuals/2000/javascript/1.5/reference/)\nNov 2000", "x": 14880, "y": 6280, "width": 240, "height": 80, "color": "3"}, {"id": "19a4291aefd0323b", "type": "text", "text": "[Dylan](https://opendylan.org/books/drm/Preface)\n1992", "x": 12000, "y": 6200, "width": 240, "height": 80, "color": "6"}, {"id": "e1a80fd1bb37389e", "type": "text", "text": "Cmm\n1992", "x": 12000, "y": 6280, "width": 240, "height": 80, "color": "3"}, {"id": "53cc6404880b749c", "type": "text", "text": "Ruby 1.6.8\nDec 2002", "x": 15600, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "de15f44d4ed54bd3", "type": "text", "text": "[Ruby 1.8](file:///Users/<USER>/Literature/CS/Languages/ruby_2004.pdf)\nAug 2003", "x": 15960, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "dfb09c7c972c753a", "type": "text", "text": "Ruby 1.6.1\nSep 2000", "x": 14880, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "d5356319b6d651fc", "type": "text", "text": "Ruby 1.6.5\nSep 2001", "x": 15240, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "1fc78359e95566d8", "type": "text", "text": "Ruby 1.3.2\nApr 1999", "x": 14520, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "31b99da9d06a216a", "type": "text", "text": "Applescript 1.3.7\n1999", "x": 14520, "y": 6480, "width": 240, "height": 80, "color": "1"}, {"id": "31017c50ee4c898e", "type": "text", "text": "Ruby 1.1 alpha 0\nAug 1997", "x": 13800, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "b68a95402d8073ec", "type": "text", "text": "Ruby 0.95\nDec 1995", "x": 13080, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "4e8ce2b4d9d0ea7f", "type": "text", "text": "Ruby\nFeb 1993", "x": 12360, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "a79cd7d25e98469d", "type": "text", "text": "Applescript\n1993", "x": 12360, "y": 6480, "width": 240, "height": 80, "color": "1"}, {"id": "012771e13edd4f14", "type": "text", "text": "Ruby 2.0\nFeb 2014", "x": 19920, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "124dd9373eb9093a", "type": "text", "text": "Ruby 3.0\nDec 2020", "x": 22080, "y": 6400, "width": 240, "height": 80, "color": "#5a2dd7"}, {"id": "88539042eb11263e", "type": "text", "text": "PHP 5.0\nJul 2004", "x": 16320, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "4c921eec6e3ded45", "type": "text", "text": "Delphi 7\nAug 2002", "x": 15600, "y": 6960, "width": 240, "height": 80, "color": "#2a7e7d"}, {"id": "624bcb2add4bb843", "type": "text", "text": "PHP 4.0\nMay 2000", "x": 14880, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "efb89277495d3966", "type": "text", "text": "Delphi 6\nMay 2001", "x": 15240, "y": 6960, "width": 240, "height": 80, "color": "#2a7e7d"}, {"id": "5168f1c3773b695a", "type": "text", "text": "Delphi 5\nAug 1999", "x": 14520, "y": 6960, "width": 240, "height": 80, "color": "#2a7e7d"}, {"id": "598170a11c764cc1", "type": "text", "text": "PHP 3.0\nJun 1998", "x": 14160, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "621062d25aaa579e", "type": "text", "text": "[PHP 2.0](https://www.php.net/manual/phpfi2.php)\nNov 1997", "x": 13800, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "9b14444d8b1b0a6b", "type": "text", "text": "Delphi\nMar 1995", "x": 13080, "y": 6960, "width": 240, "height": 80, "color": "#2a7e7d"}, {"id": "54d30afabcf9b14d", "type": "text", "text": "PHP/FI\n1995", "x": 13080, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "37b864a2f4b80e84", "type": "text", "text": "PHP 5.6\nAug 2014", "x": 19920, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "dbb708209916a992", "type": "text", "text": "PHP 7.0\nDec 2015", "x": 20280, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "1644f236f6d21fe1", "type": "text", "text": "PHP 8.0\nNov 2020", "x": 22080, "y": 7040, "width": 240, "height": 80, "color": "#8ecccb"}, {"id": "56e189cc1529d962", "type": "text", "text": "[Python 2.0](https://docs.python.org/release/2.0/ref/ref.html)\nOct 2000", "x": 14880, "y": 5920, "width": 240, "height": 80, "color": "#c63a0c"}, {"id": "58ccc548eb1b71e2", "type": "text", "text": "NetRexx 1.150\nJul 1999", "x": 14520, "y": 6080, "width": 240, "height": 80, "color": "3"}, {"id": "adeecbfd5706a4fd", "type": "text", "text": "[NetRexx](file:///Users/<USER>/Literature/CS/Languages/netrexx_1997.pdf)\n1991", "x": 11640, "y": 6080, "width": 240, "height": 80, "color": "3"}, {"id": "ea9ec3da93fbe55f", "type": "text", "text": "[Oak](file:///Users/<USER>/Literature/CS/Languages/oak_1994.pdf)\nJun 1991", "x": 11640, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "ac54db3b9e631695", "type": "text", "text": "[Java 1](https://titanium.cs.berkeley.edu/doc/java-langspec-1.0/)\nMay 1995", "x": 13080, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "3896d32d8f25e590", "type": "text", "text": "[Java 2 (v1.2)](file://https://titanium.cs.berkeley.edu/doc/java-langspec-1.0/)\nDec 1998", "x": 14160, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "890f5ce014673fe5", "type": "text", "text": "Java 2 (v1.4)\n2002", "x": 15600, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "6f271025b6514abb", "type": "text", "text": "Java 2 (v1.3)\nMay 2000", "x": 14880, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "9201b7669aad7ed8", "type": "text", "text": "[Java SE 6](file:///Users/<USER>/Literature/CS/Languages/java_2005.pdf)\n2005", "x": 16680, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "64389e58a561f13b", "type": "text", "text": "Java 2 (v1.5)\n2004", "x": 16320, "y": 6000, "width": 240, "height": 80, "color": "2"}, {"id": "77c7f786fe2098f9", "type": "text", "text": "(JScript)\n2000", "x": 14880, "y": 7320, "width": 240, "height": 80, "color": "3"}, {"id": "b7653d457dc0ac31", "type": "text", "text": "VBScript 2.0\n1997", "x": 13800, "y": 7120, "width": 240, "height": 80, "color": "1"}, {"id": "fe129f12c3dd5ad0", "type": "text", "text": "VBScript\n1995", "x": 13080, "y": 7120, "width": 240, "height": 80, "color": "1"}, {"id": "116cbc3e5cee49c1", "type": "text", "text": "JScript\nMay 1996", "x": 13440, "y": 7320, "width": 240, "height": 80, "color": "3"}, {"id": "67bc870d88f5ef05", "type": "text", "text": "[ECMAScript ed3](file:///Users/<USER>/Literature/CS/Languages/ecmascript_1999.pdf)\nDec 1999", "x": 14520, "y": 7520, "width": 240, "height": 80, "color": "1"}, {"id": "0d48d40b60fbba43", "type": "text", "text": "[ECMAScript](file:///Users/<USER>/Literature/CS/Languages/ecmascript_1997.pdf)\nJun 1997", "x": 13800, "y": 7520, "width": 240, "height": 80, "color": "1"}, {"id": "db8b6c5cfbd8f39b", "type": "text", "text": "[Objective Caml](https://web.archive.org/web/19961128020423/http://pauillac.inria.fr/ocaml/htmlman/)\n1996", "x": 13440, "y": 7400, "width": 240, "height": 80, "color": "2"}, {"id": "4425238972f5c331", "type": "text", "text": "C# 2.0\nNov 2005", "x": 16680, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "e6b999b3475a8865", "type": "text", "text": "C# (ISO)\nMar 2003", "x": 15960, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "848ea3d3ab1ac8f1", "type": "text", "text": "Actionscript 2.0\nSep 2003", "x": 15960, "y": 7960, "width": 240, "height": 80, "color": "4"}, {"id": "4d6b94bae22ddd2e", "type": "text", "text": "C#\nJun 2000", "x": 14880, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "b7e62094b9ac003d", "type": "text", "text": "ActionScript\nJul 2000", "x": 14880, "y": 7960, "width": 240, "height": 80, "color": "4"}, {"id": "ef33f23ec66a377c", "type": "text", "text": "[C# (ECMA)](file://file:///Users/<USER>/Literature/CS/Languages/csharp_2001.pdf)\nDec 2001", "x": 15240, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "8be9a1e255964736", "type": "text", "text": "C# 13.0\n2024", "x": 23520, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "0b32f5cc6d1ab3c1", "type": "text", "text": "C# 6.0\n2015", "x": 20280, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "312f4500eef85efe", "type": "text", "text": "C# 7.0\n2017", "x": 21000, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "48b4a01ccfd9708f", "type": "text", "text": "C# 5.0\n2012", "x": 19200, "y": 7880, "width": 240, "height": 80, "color": "#99689c"}, {"id": "f2e75a4126a8eebc", "type": "text", "text": "Object Pascal\n1985", "x": 9480, "y": 4400, "width": 240, "height": 80, "color": "#8f385b"}, {"id": "977e2ea3d413313b", "type": "text", "text": "[Clojure 1.0](https://web.archive.org/web/20090508062048/http://clojure.org/reference)\nMay 2009", "x": 18120, "y": 9040, "width": 240, "height": 80, "color": "5"}, {"id": "211ef1ebaa714e4b", "type": "text", "text": "CoffeeScript\nDec 2009", "x": 18120, "y": 9280, "width": 240, "height": 80, "color": "5"}, {"id": "0f8ff1e109fd91ed", "type": "text", "text": "[CoffeeScript 0.5](https://web.archive.org/web/20100301132006/http://jashkenas.github.com/coffee-script/)\nFeb 2010", "x": 18480, "y": 9280, "width": 240, "height": 80, "color": "5"}, {"id": "404c27a6799809f6", "type": "text", "text": "[Clojure](https://web.archive.org/web/20080516070213/http://www.clojure.org/reference/)\nOct 2007", "x": 17400, "y": 9040, "width": 240, "height": 80, "color": "5"}, {"id": "aa955875eebdf9e4", "type": "text", "text": "Clojure 1.12.0\nSep 2024", "x": 23520, "y": 9040, "width": 240, "height": 80, "color": "5"}, {"id": "d0e12572d9992565", "type": "text", "text": "[CoffeeScript 2.0.0](https://coffeescript.org/#language)\nSep 2017", "x": 21000, "y": 9280, "width": 240, "height": 80, "color": "5"}, {"id": "befbe71a20a81577", "type": "text", "text": "[Coffeescript 1.0.0](https://coffeescript.org/v1/)\n2011", "x": 18840, "y": 9280, "width": 240, "height": 80, "color": "5"}, {"id": "9d2ebdca1296a70d", "type": "text", "text": "Go\nNov 2009", "x": 18120, "y": 9360, "width": 240, "height": 80, "color": "4"}, {"id": "0881f5616edb92cd", "type": "text", "text": "Go 1.20\nFeb 2023", "x": 23160, "y": 9360, "width": 240, "height": 80, "color": "4"}, {"id": "a9221cb0f64c630c", "type": "text", "text": "Crystal 1.16.0\nApr 2025", "x": 23880, "y": 10520, "width": 240, "height": 80, "color": "3"}, {"id": "af20d00003e241d9", "type": "text", "text": "[Crystal 0.1.0](https://crystal-lang.org/2014/06/19/crystal-0.1.0-released/)\nJun 2014", "x": 19920, "y": 10520, "width": 240, "height": 80, "color": "3"}, {"id": "a21b9982f5487a1f", "type": "text", "text": "Crystal 0.31.0\nSep 2019", "x": 21720, "y": 10520, "width": 240, "height": 80, "color": "3"}, {"id": "df30a37c6503152f", "type": "text", "text": "Go 1.11\nAug 2018", "x": 21360, "y": 9360, "width": 240, "height": 80, "color": "4"}, {"id": "3beaeaa8aeed5b4d", "type": "text", "text": "Crystal 1.0.0\nMar 2021", "x": 22440, "y": 10520, "width": 240, "height": 80, "color": "3"}, {"id": "ddfb4253929db68f", "type": "text", "text": "[Go 1.0](file:///Users/<USER>/Literature/CS/Languages/go-release-branch.go1/doc)\nMar 2012", "x": 19200, "y": 9360, "width": 240, "height": 80, "color": "4"}, {"id": "f4564a09fc419449", "type": "text", "text": "[Elixir](https://hexdocs.pm/elixir/1.18.4/Kernel.html)\nMay 2025", "x": 23880, "y": 9800, "width": 240, "height": 80, "color": "3"}, {"id": "da94bfd801327a06", "type": "text", "text": "[Dart 1.11 (ECMA)](file:///Users/<USER>/Literature/CS/Languages/dart_2015.pdf)\nDec 2015", "x": 20280, "y": 9600, "width": 240, "height": 80, "color": "1"}, {"id": "df2054c158055f86", "type": "text", "text": "[Elixir 1.6.0](https://hexdocs.pm/elixir/1.6.0/Kernel.html)\nJan 2018", "x": 21360, "y": 9800, "width": 240, "height": 80, "color": "3"}, {"id": "a5ecc8d29d99b62a", "type": "text", "text": "[Dart 2.10](file:///Users/<USER>/Literature/CS/Languages/dart_2021.pdf)\nApr 2021", "x": 22440, "y": 9600, "width": 240, "height": 80, "color": "1"}, {"id": "7a3081e419c922e8", "type": "text", "text": "[Elixir](https://hexdocs.pm/elixir/1.0.4/Kernel.html)\n2012", "x": 19200, "y": 9800, "width": 240, "height": 80, "color": "3"}, {"id": "9427e775014f5e0e", "type": "text", "text": "[Dart](file:///Users/<USER>/Literature/CS/Languages/dart_2011.pdf)\nOct 2011", "x": 18840, "y": 9600, "width": 240, "height": 80, "color": "1"}, {"id": "13dfe0e5d60ff3f9", "type": "text", "text": "[F# 2.0](file:///Users/<USER>/Literature/CS/Languages/fsharp_2012.pdf)\nAug 2010", "x": 18480, "y": 8800, "width": 240, "height": 80, "color": "3"}, {"id": "74987209c71e3abb", "type": "text", "text": "[Scala](file:///Users/<USER>/Literature/CS/Languages/scala-1.0.0-b5/doc/reference)\nJan 2004", "x": 16320, "y": 8520, "width": 240, "height": 80, "color": "5"}, {"id": "bcb40398fe58c522", "type": "text", "text": "F#\n2005", "x": 16680, "y": 8800, "width": 240, "height": 80, "color": "3"}, {"id": "16c0a8142b9f9671", "type": "text", "text": "[Scala 3.4](https://scala-lang.org/files/archive/spec/3.4/)\nFeb 2024", "x": 23520, "y": 8520, "width": 240, "height": 80, "color": "5"}, {"id": "4a2eb6e04e97a694", "type": "text", "text": "[Scala 2.13](https://scala-lang.org/files/archive/spec/2.13/)\nJun 2019", "x": 21720, "y": 8520, "width": 240, "height": 80, "color": "5"}, {"id": "9839bec3abf0a4fd", "type": "text", "text": "F# 5.0\nNov 2020", "x": 22080, "y": 8800, "width": 240, "height": 80, "color": "3"}, {"id": "21fc09aa75de425a", "type": "text", "text": "[GLSL 4.00](file:///Users/<USER>/Literature/CS/Languages/glsl_2010.pdf)\nJul 2010", "x": 18480, "y": 8600, "width": 240, "height": 80, "color": "1"}, {"id": "6777c944f3b30628", "type": "text", "text": "[GLSL](file:///Users/<USER>/Literature/CS/Languages/glsl_2004.pdf)\nApr 2004", "x": 16320, "y": 8600, "width": 240, "height": 80, "color": "1"}, {"id": "b0e6c7ae90b2b156", "type": "text", "text": "HLSL\n2002", "x": 15600, "y": 8280, "width": 240, "height": 80, "color": "1"}, {"id": "d3d7c2ee5c9ae503", "type": "text", "text": "HLSL 2021\n2023", "x": 23160, "y": 8280, "width": 240, "height": 80, "color": "1"}, {"id": "59a53ebc6331f98f", "type": "text", "text": "GLSL 4.60.5\nJun 2018", "x": 21360, "y": 8600, "width": 240, "height": 80, "color": "1"}, {"id": "a06e66ff2c8cadbb", "type": "text", "text": "[JSON (RC 4627)](file:///Users/<USER>/Literature/CS/Languages/json_2006.pdf)\nJul 2006", "x": 17040, "y": 8080, "width": 240, "height": 80, "color": "5"}, {"id": "5ea953ff6ecb5f55", "type": "text", "text": "JSON\nApr 2001", "x": 15240, "y": 8080, "width": 240, "height": 80, "color": "5"}, {"id": "223f3e25db2dba97", "type": "text", "text": "[GraphQL 2015](https://spec.graphql.org/July2015/)\nJul 2015", "x": 20280, "y": 9880, "width": 240, "height": 80, "color": "4"}, {"id": "42c17de94d9e8e09", "type": "text", "text": "GraphQL 2018\n2018", "x": 21360, "y": 9880, "width": 240, "height": 80, "color": "4"}, {"id": "048da021f06b5e90", "type": "text", "text": "[JSON (ECMA 404 ed3)](file:///Users/<USER>/Literature/CS/Languages/json_2017.pdf)\nDec 2017", "x": 21000, "y": 8080, "width": 280, "height": 80, "color": "5"}, {"id": "ab97f4a7122f26f7", "type": "text", "text": "GraphQL\n2021", "x": 22440, "y": 9880, "width": 240, "height": 80, "color": "4"}, {"id": "0cf1cef6c531e05a", "type": "text", "text": "GraphQL\n2012", "x": 19200, "y": 9880, "width": 240, "height": 80, "color": "4"}, {"id": "6489e529978b5b94", "type": "text", "text": "[JSON (ECMA 404 ed1)](file:///Users/<USER>/Literature/CS/Languages/json_2013.pdf)\nOct 2013", "x": 19560, "y": 8080, "width": 280, "height": 80, "color": "5"}, {"id": "e4be1f6f5d64fc91", "type": "text", "text": "Pascal AFNOR\n1983", "x": 8760, "y": 1520, "width": 240, "height": 80, "color": "2"}, {"id": "3fe2b5685b2c9a14", "type": "text", "text": "ABAP 7.0\n2006", "x": 17040, "y": 4080, "width": 240, "height": 80, "color": "2"}, {"id": "1543e3448d3337e8", "type": "text", "text": "ABAP 6.40\n2004", "x": 16320, "y": 4080, "width": 240, "height": 80, "color": "2"}, {"id": "93b280215ca85ceb", "type": "text", "text": "ABAP 4.6C\nMay 2000", "x": 14880, "y": 4080, "width": 240, "height": 80, "color": "2"}, {"id": "1fd4d16d9083326b", "type": "text", "text": "ABAP\n1983", "x": 8760, "y": 4080, "width": 240, "height": 80, "color": "2"}, {"id": "4b4788f11a97449b", "type": "text", "text": "Cedar\n1983", "x": 8760, "y": 4000, "width": 240, "height": 80, "color": "#eb2ddc"}, {"id": "75cbf67be9d19646", "type": "text", "text": "[SQL](file:///Users/<USER>/Literature/CS/Languages/sql_1974.pdf)\n1973", "x": 5160, "y": 2040, "width": 240, "height": 80, "color": "#6e2e7f"}, {"id": "51970f5cd6ed434a", "type": "text", "text": "[SQL-86](file:///Users/<USER>/Literature/CS/Languages/sql_1986.pdf)\n1986", "x": 9840, "y": 2040, "width": 240, "height": 80, "color": "#6e2e7f"}, {"id": "8b40f8b7f52d6086", "type": "text", "text": "SQL2\n1992", "x": 12000, "y": 2040, "width": 240, "height": 80, "color": "#6e2e7f"}, {"id": "065d3d4032874a83", "type": "text", "text": "SQL3\n1999", "x": 14520, "y": 2040, "width": 240, "height": 80, "color": "#6e2f7f"}, {"id": "9d90620a710b90b9", "type": "text", "text": "SQL:2023\n2023", "x": 23160, "y": 2040, "width": 240, "height": 80, "color": "#6e2e7f"}, {"id": "5a598d7bf6c42f2c", "type": "text", "text": "[Dart 3.0](file:///Users/<USER>/Literature/CS/Languages/dart_2025(draft).pdf)\nMay 2023", "x": 23160, "y": 9600, "width": 240, "height": 80, "color": "1"}, {"id": "eed02d108e6dad2e", "type": "text", "text": "Erlang\n1986", "x": 9840, "y": 4600, "width": 240, "height": 80, "color": "#9d3e0b"}, {"id": "ffae12eaa3e2ccc6", "type": "text", "text": "[Erlang 5.3](file:///Users/<USER>/Literature/CS/Languages/erlang_1999.pdf)\n1999", "x": 14520, "y": 4600, "width": 240, "height": 80, "color": "#9d3e0b"}, {"id": "0118135e12da1bf5", "type": "text", "text": "[Erlang 5.6](file:///Users/<USER>/Literature/CS/Languages/erlang_2006.pdf)\n2006", "x": 17040, "y": 4600, "width": 240, "height": 80, "color": "#9d3e0b"}, {"id": "781e60da7e8ab2e6", "type": "text", "text": "[Erlang 28.0.1](https://www.erlang.org/doc/readme.html)\n2025", "x": 23880, "y": 4600, "width": 240, "height": 80, "color": "#9d3e0b"}, {"id": "b07b5af5a8c1927f", "type": "text", "text": "F# 9.0\nNov 2024", "x": 23520, "y": 8800, "width": 240, "height": 80, "color": "3"}, {"id": "a72d8d9711be19f2", "type": "text", "text": "HLSL Shader Model 5.1\n2015", "x": 20280, "y": 8280, "width": 240, "height": 80, "color": "1"}, {"id": "5d002e1cf03785bd", "type": "text", "text": "[HLSL Shader Model 6.0](https://learn.microsoft.com/en-us/windows/win32/direct3dhlsl/hlsl-shader-model-6-0-features-for-direct3d-12)\n2016", "x": 20640, "y": 8280, "width": 280, "height": 80, "color": "1"}, {"id": "5cda45bd40761d9b", "type": "text", "text": "Groovy 1.0\nJan 2007", "x": 17400, "y": 8680, "width": 240, "height": 80, "color": "4"}, {"id": "fd926a42e3504bed", "type": "text", "text": "Groovy\nMar 2004", "x": 16320, "y": 8680, "width": 240, "height": 80, "color": "4"}, {"id": "a8797e67856f7a7c", "type": "text", "text": "[Groovy 4.0](http://docs.groovy-lang.org/docs/groovy-4.0.0/html/)\nJan 2022", "x": 22800, "y": 8680, "width": 240, "height": 80, "color": "4"}, {"id": "061f56f8d5b215ae", "type": "text", "text": "[HTML 5.0](https://www.w3.org/TR/2014/REC-html5-20141028/)\nOct 2014", "x": 19920, "y": 6560, "width": 240, "height": 80, "color": "4"}, {"id": "dfe2b9aa04e2b9aa", "type": "text", "text": "[Groovy 3.0](http://docs.groovy-lang.org/docs/groovy-3.0.0/html/)\nFeb 2020", "x": 22080, "y": 8680, "width": 240, "height": 80, "color": "4"}, {"id": "4f54ccf8f003e4e2", "type": "text", "text": "XML 1.0 (ed5)\nNov 2008", "x": 17760, "y": 7640, "width": 240, "height": 80, "color": "3"}, {"id": "ad882e76279e4f27", "type": "text", "text": "XML 1.1 (ed2)\nAug 2006", "x": 17040, "y": 7640, "width": 240, "height": 80, "color": "3"}, {"id": "bf2099d3d9ab0d0e", "type": "text", "text": "[Julia 1.11](https://docs.julialang.org/en/v1/)\nApr 2025", "x": 23880, "y": 9960, "width": 240, "height": 80, "color": "6"}, {"id": "35501f0be5c6700e", "type": "text", "text": "[Julia 1.0](https://docs.julialang.org/en/v1.0/)\n2018", "x": 21360, "y": 9960, "width": 240, "height": 80, "color": "6"}, {"id": "3a8d2c71a1bc3039", "type": "text", "text": "[<PERSON>](https://docs.julialang.org/en/v0.3/)\n2012", "x": 19200, "y": 9960, "width": 240, "height": 80, "color": "6"}, {"id": "300c92e7d2a8008d", "type": "text", "text": "Mathematic 14.2.0\nJan 2025", "x": 23880, "y": 5480, "width": 240, "height": 80, "color": "4"}, {"id": "071e22882fe6f63d", "type": "text", "text": "MATLAB 25.1\n2025", "x": 23880, "y": 4280, "width": 240, "height": 80, "color": "4"}, {"id": "da56b891283079dd", "type": "text", "text": "R 4.5.1\n2025", "x": 23880, "y": 6640, "width": 240, "height": 80, "color": "2"}, {"id": "b0ebb850902e5a4b", "type": "text", "text": "Kotlin 2.0\nAug 2024", "x": 23520, "y": 9680, "width": 240, "height": 80, "color": "2"}, {"id": "ed29a9785d664efa", "type": "text", "text": "Kotlin 1.0\nFeb 2016", "x": 20640, "y": 9680, "width": 240, "height": 80, "color": "2"}, {"id": "9852d7c16dee2b5a", "type": "text", "text": "<PERSON><PERSON><PERSON> 2011", "x": 18840, "y": 9680, "width": 240, "height": 80, "color": "2"}, {"id": "7558fbbec59a2eae", "type": "text", "text": "[Groovy 1.7](http://docs.groovy-lang.org/docs/groovy-1.7.0/html/)\nDec 2009", "x": 18120, "y": 8680, "width": 240, "height": 80, "color": "4"}, {"id": "904c8dab955666cb", "type": "text", "text": "[Groovy 2.0](http://docs.groovy-lang.org/docs/groovy-2.0.0/html/)\nJul 2012", "x": 19200, "y": 8680, "width": 240, "height": 80, "color": "4"}, {"id": "df00c23eaf0f9864", "type": "text", "text": "[HTML 2.0](file:///Users/<USER>/Literature/CS/Languages/html_1995.pdf)\n1995", "x": 13080, "y": 6560, "width": 240, "height": 80, "color": "4"}, {"id": "7d6163007a3c9b5b", "type": "text", "text": "[HTML](https://datatracker.ietf.org/doc/html/draft-ietf-iiir-html-00)\n1993", "x": 12360, "y": 6560, "width": 240, "height": 80, "color": "4"}, {"id": "a06eed9b4410f7ce", "type": "text", "text": "[HTML 3.2](https://www.w3.org/TR/2018/SPSD-html32-20180315/)\nJan 1997", "x": 13760, "y": 6560, "width": 160, "height": 80, "color": "4"}, {"id": "33a316e45de25c27", "type": "text", "text": "[SGML](file:///Users/<USER>/Literature/CS/Languages/sgml_1986.pdf)\n1986", "x": 9840, "y": 4680, "width": 240, "height": 80, "color": "#2f7f24"}, {"id": "a858f3d3cf18bd30", "type": "text", "text": "[GML](https://www.ibm.com/docs/en/zos/2.1.0?topic=books-appendix-c-gml-starter-set-tag-reference)\n1969", "x": 3720, "y": 1400, "width": 240, "height": 80, "color": "#125d97"}, {"id": "01904ed5d4245b88", "type": "text", "text": "[XML 1.0](https://www.w3.org/TR/1998/REC-xml-19980210)\nFeb 1998", "x": 14160, "y": 7640, "width": 240, "height": 80, "color": "3"}, {"id": "ca01d4a6afaea3c2", "type": "text", "text": "[XML 1.1](https://www.w3.org/TR/2002/CR-xml11-20021015/)\nOct 2002", "x": 15600, "y": 7640, "width": 240, "height": 80, "color": "3"}, {"id": "4d9d431946c00799", "type": "text", "text": "Mathematica\n1988", "x": 10560, "y": 5480, "width": 240, "height": 80, "color": "4"}, {"id": "5b8121e38c8bc7c8", "type": "text", "text": "MATLAB\n1984", "x": 9120, "y": 4280, "width": 240, "height": 80, "color": "4"}, {"id": "c9b597feebc31bab", "type": "text", "text": "S\n1976", "x": 6240, "y": 2560, "width": 240, "height": 80, "color": "1"}, {"id": "ca5c149918a677c7", "type": "text", "text": "S-PLUS\n1988", "x": 10560, "y": 2560, "width": 240, "height": 80, "color": "1"}, {"id": "24c6b4c401556241", "type": "text", "text": "R\n1993", "x": 12360, "y": 6640, "width": 240, "height": 80, "color": "2"}, {"id": "13c48ce0ccc4e4eb", "type": "text", "text": "Nimrod\n2008", "x": 17760, "y": 9160, "width": 240, "height": 80, "color": "1"}, {"id": "ffd33d64cfb9372b", "type": "text", "text": "[Mojo](https://docs.modular.com/mojo/manual/get-started/)\n2023", "x": 23160, "y": 11680, "width": 240, "height": 80, "color": "2"}, {"id": "7fa16c3efd7a8ec8", "type": "text", "text": "[Nim 2.0](https://nim-lang.org/docs/manual.html)\nAug 2023", "x": 23160, "y": 9160, "width": 240, "height": 80, "color": "1"}, {"id": "7943403d25c7f545", "type": "text", "text": "Nim 1.0\nSep 2019", "x": 21720, "y": 9160, "width": 240, "height": 80, "color": "1"}, {"id": "5978ad6799320393", "type": "text", "text": "Nim 0.10.2\nDec 2012", "x": 19200, "y": 9160, "width": 240, "height": 80, "color": "1"}, {"id": "407490ec330c92a6", "type": "text", "text": "[Racket 5.0](https://web.archive.org/web/20101103221251/http://docs.racket-lang.org/reference/index.html)\nJun 2010", "x": 18480, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "5f19433353b5f355", "type": "text", "text": "PLT Scheme 4.0\nJun 2008", "x": 17760, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "9a0b82dc07236b44", "type": "text", "text": "PLT Scheme 200\n2001", "x": 15240, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "c5908cfb4a38dc04", "type": "text", "text": "Racket 6.0\nFeb 2014", "x": 19920, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "68d45a91eaf2c566", "type": "text", "text": "ReasonML\n2016", "x": 20640, "y": 10840, "width": 240, "height": 80}, {"id": "104fe1eb8bbdd840", "type": "text", "text": "Racket 7.0\nMay 2018", "x": 21360, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "7aa8a2dbe0c1acdf", "type": "text", "text": "[Racket 8.0](https://docs.racket-lang.org/reference/index.html)\nFeb 2021", "x": 22440, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "993ddc59d9338067", "type": "text", "text": "Swift 6.0\nSep 2024", "x": 23520, "y": 10440, "width": 240, "height": 80, "color": "6"}, {"id": "56d0a32c54410949", "type": "text", "text": "Solidity 0.80.30\nMay 2025", "x": 23880, "y": 10360, "width": 240, "height": 80, "color": "2"}, {"id": "f1b64a55016f7858", "type": "text", "text": "Solidity\nAug 2014", "x": 19920, "y": 10360, "width": 240, "height": 80, "color": "2"}, {"id": "fc3ea3a88eaaed35", "type": "text", "text": "Swift\nJun 2014", "x": 19920, "y": 10440, "width": 240, "height": 80, "color": "6"}, {"id": "192aa320f03d5a26", "type": "text", "text": "Swift 5.0\nMar 2019", "x": 21720, "y": 10440, "width": 240, "height": 80, "color": "6"}, {"id": "17b9697bcd50fdfa", "type": "text", "text": "Swift 4.0\nSep 2017", "x": 21000, "y": 10440, "width": 240, "height": 80, "color": "6"}, {"id": "05b1bfdc73169483", "type": "text", "text": "Sather 1.2.3\n2007", "x": 17400, "y": 5800, "width": 240, "height": 80, "color": "2"}, {"id": "7fed863ddcabbad4", "type": "text", "text": "Rust 1.88.0\nJun 2025", "x": 23880, "y": 10040, "width": 240, "height": 80, "color": "1"}, {"id": "93176596c697867b", "type": "text", "text": "[Rust 1.0](https://doc.rust-lang.org/std/index.html)\nMay 2015", "x": 20280, "y": 10040, "width": 240, "height": 80, "color": "1"}, {"id": "0bd4da4f6166ea4b", "type": "text", "text": "Rust\nJan 2012", "x": 19200, "y": 10040, "width": 240, "height": 80, "color": "1"}, {"id": "5cb574c05267f255", "type": "text", "text": "TypeScript 5.0\nMar 2023", "x": 23160, "y": 10120, "width": 240, "height": 80, "color": "5"}, {"id": "6876efd642d62a97", "type": "text", "text": "[WebAssembly 2.0](file:///Users/<USER>/Literature/CS/Languages/webassembly_2025.pdf)\nJun 2025", "x": 23880, "y": 10960, "width": 240, "height": 80, "color": "4"}, {"id": "0b238612fb0717b4", "type": "text", "text": "[TypeScript 1.0](file:///Users/<USER>/Literature/CS/Languages/typescript_2014.pdf)\nApr 2014", "x": 19920, "y": 10120, "width": 240, "height": 80, "color": "5"}, {"id": "a9edff0b5ce315e8", "type": "text", "text": "TypeScript 4.0\nAug 2020", "x": 22080, "y": 10120, "width": 240, "height": 80, "color": "5"}, {"id": "33a452c2fa1d1983", "type": "text", "text": "TypeScript 2.0\nSep 2016", "x": 20640, "y": 10120, "width": 240, "height": 80, "color": "5"}, {"id": "6e441c0b484e3e84", "type": "text", "text": "TypeScript 3.0\nJul 2018", "x": 21360, "y": 10120, "width": 240, "height": 80, "color": "5"}, {"id": "6795b336f97e14f8", "type": "text", "text": "[WebAssembly](https://www.w3.org/TR/wasm-core-1/)\nMar 2017", "x": 21000, "y": 10960, "width": 240, "height": 80, "color": "4"}, {"id": "baf656c6fe8b3f8d", "type": "text", "text": "[TypeScript](file:///Users/<USER>/Literature/CS/Languages/typescript_2012.pdf)\nOct 2012", "x": 19200, "y": 10120, "width": 240, "height": 80, "color": "5"}, {"id": "48f645ba98797e7a", "type": "text", "text": "Kotlin 1.3\nApr 2020", "x": 22080, "y": 9680, "width": 240, "height": 80, "color": "2"}, {"id": "908091c0f2215d44", "type": "text", "text": "Lua 5.0\nApr 2003", "x": 15960, "y": 6720, "width": 240, "height": 80, "color": "5"}, {"id": "1e879ca6dd1aefd5", "type": "text", "text": "Lua 4.0\nNov 2000", "x": 14880, "y": 6720, "width": 240, "height": 80, "color": "5"}, {"id": "08bae566db2b440a", "type": "text", "text": "Lua 3.0\nJul 1997", "x": 13800, "y": 6720, "width": 240, "height": 80, "color": "5"}, {"id": "296e8f5e4e2fdb6a", "type": "text", "text": "Lua 2.1\nFeb 1995", "x": 13080, "y": 6720, "width": 240, "height": 80, "color": "5"}, {"id": "e682cf7c8422a754", "type": "text", "text": "Lua 1.0\nJul 1993", "x": 12360, "y": 6720, "width": 240, "height": 80, "color": "5"}, {"id": "0c9ffb04865c2238", "type": "text", "text": "Lua 5.4\nJun 2020", "x": 22080, "y": 6720, "width": 240, "height": 80, "color": "5"}, {"id": "0e0dd418219ccfda", "type": "text", "text": "PLT Scheme 100\n1998", "x": 14160, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "e92e2fcb3c57af45", "type": "text", "text": "PLT Scheme\nJan 1995", "x": 13080, "y": 7200, "width": 240, "height": 80, "color": "1"}, {"id": "a8fa0fba8b69ab60", "type": "text", "text": "OCaml 2.00\nAug. 1998", "x": 14160, "y": 7400, "width": 240, "height": 80, "color": "2"}, {"id": "8afa1ecd55db0c5b", "type": "text", "text": "OCaml 3.00\nApr 2001", "x": 15240, "y": 7400, "width": 240, "height": 80, "color": "2"}, {"id": "093618ebf485a513", "type": "text", "text": "OCaml\nJul 2012", "x": 19200, "y": 7400, "width": 240, "height": 80, "color": "2"}, {"id": "09b3854769bc18c0", "type": "text", "text": "OCaml 5.0\nDec 2022", "x": 22800, "y": 7400, "width": 240, "height": 80, "color": "2"}, {"id": "112518d84753389f", "type": "text", "text": "[Swift 2.2](file:///Users/<USER>/Literature/CS/Languages/swift_2015.epub)\nMar 2016", "x": 20640, "y": 10440, "width": 240, "height": 80, "color": "6"}, {"id": "054f555173cbbd93", "type": "text", "text": "[Sather 1.2](https://www.gnu.org/software/sather/docs-1.2/specification/sather-specification.html)\n1999", "x": 14520, "y": 5800, "width": 240, "height": 80, "color": "2"}, {"id": "1bfe608a58a7dd8b", "type": "text", "text": "Sather\n1990", "x": 11280, "y": 5800, "width": 240, "height": 80, "color": "2"}, {"id": "eae3cda390066fd1", "type": "text", "text": "[YAML 1.2.0](https://yaml.org/spec/1.2.0/)\nJul 2009", "x": 18040, "y": 8160, "width": 200, "height": 80, "color": "3"}, {"id": "e1adb42eecb8cf6f", "type": "text", "text": "[YAML 1.0](https://yaml.org/spec/1.0/)\nJan 2004", "x": 16320, "y": 8160, "width": 240, "height": 80, "color": "3"}, {"id": "d680d1fb55233ab9", "type": "text", "text": "[YAML 1.1](https://yaml.org/spec/1.1/)\nJan 2005", "x": 16680, "y": 8160, "width": 240, "height": 80, "color": "3"}, {"id": "8d9c29b6030ce739", "type": "text", "text": "[YAML](https://yaml.org/spec/history/2001-05-26.html)\nMay 2001", "x": 15240, "y": 8160, "width": 240, "height": 80, "color": "3"}, {"id": "71b7a5d0963e8fb9", "type": "text", "text": "[Zig 0.14.1](https://ziglang.org/documentation/0.14.1/)\n2025", "x": 23880, "y": 10760, "width": 240, "height": 80, "color": "1"}, {"id": "ce4ad1b916634c2b", "type": "text", "text": "[Zig](https://andrewkelley.me/post/intro-to-zig.html#resources)\nFeb 2016", "x": 20640, "y": 10760, "width": 240, "height": 80, "color": "1"}, {"id": "8efe7c783a6c9829", "type": "text", "text": "[Zig 0.1.1](https://ziglang.org/documentation/0.1.1/)\n2017", "x": 21000, "y": 10760, "width": 240, "height": 80, "color": "1"}, {"id": "2bde5c7a605fa79b", "type": "text", "text": "[YAML 1.2.2](https://yaml.org/spec/1.2.2/)\nOct 2021", "x": 22440, "y": 8160, "width": 240, "height": 80, "color": "3"}, {"id": "28f9764c0f2ac08e", "type": "text", "text": "[YAML 1.2.1](https://yaml.org/spec/1.2.1/)\nOct 2009", "x": 18240, "y": 8160, "width": 200, "height": 80, "color": "3"}, {"id": "b3d658daaec94691", "type": "text", "text": "[HTML 4.0](file:///Users/<USER>/Literature/CS/Languages/html_1997.pdf)\nDec 1997", "x": 13920, "y": 6560, "width": 160, "height": 80, "color": "4"}, {"id": "31e228ae0869ab23", "type": "text", "text": "Tcl/Tk\nend 1988", "x": 10680, "y": 5240, "width": 160, "height": 80, "color": "#5233f0"}, {"id": "45df154795344164", "type": "text", "text": "Tcl/Tk 8.2.3\nDec 1999", "x": 14640, "y": 5240, "width": 160, "height": 80, "color": "#5233f0"}, {"id": "53bfc36400609496", "type": "text", "text": "[JavaScript](https://web.archive.org/web/20000510182838/http://developer.netscape.com/docs/manuals/communicator/jsref/index.htm)\nDec 1995", "x": 13260, "y": 6280, "width": 160, "height": 80, "color": "3"}, {"id": "d120440c3d5019c9", "type": "text", "text": "LiveScript\n1995", "x": 13020, "y": 6280, "width": 160, "height": 80, "color": "3"}, {"id": "e553d4d95a0ca4d6", "type": "text", "text": "[SASL](file:///Users/<USER>/Literature/CS/Languages/sasl_1976.pdf)\n1976", "x": 6240, "y": 2640, "width": 240, "height": 80, "color": "5"}, {"id": "f4c56070538c7cbe", "type": "text", "text": "[occam](file:///Users/<USER>/Literature/CS/Languages/occam_1983.pdf)\n1983", "x": 8760, "y": 4160, "width": 240, "height": 80, "color": "#0c5eac"}, {"id": "f04c2a8366585388", "type": "text", "text": "occam 2\n1987", "x": 10200, "y": 4160, "width": 240, "height": 80, "color": "#0c5eac"}, {"id": "48eb9d0238c2b2c9", "type": "text", "text": "occam 2.1\n1994", "x": 12720, "y": 4160, "width": 240, "height": 80, "color": "#0c5eac"}, {"id": "d74a046ba5206b95", "type": "text", "text": "[Arith-Matic](file:///Users/<USER>/Literature/CS/Languages/math-arith-matics_1957.pdf)\n1955", "x": -1440, "y": -1400, "width": 180, "height": 80, "color": "#9536a3"}, {"id": "9adbe19d9f4a40e1", "type": "text", "text": "[Laning and Zierler System](file:///Users/<USER>/Literature/CS/Languages/whirlwind1_1954.pdf)\n1952", "x": -2420, "y": -1280, "width": 280, "height": 80, "color": "#a8d629"}, {"id": "1b5a3cbe9cb737f7", "type": "text", "text": "A-0\n1951", "x": -2760, "y": -1400, "width": 240, "height": 80, "color": "#c240d4"}, {"id": "ffc27b4e528c5146", "type": "text", "text": "[COMTRAN](file:///Users/<USER>/Literature/CS/Languages/comtran_1960.pdf)\n1957", "x": -600, "y": -600, "width": 240, "height": 80, "color": "4"}, {"id": "7518674cd0becd37", "type": "text", "text": "[Speedcoding](file:///Users/<USER>/Literature/CS/Languages/speedcoding_1953.pdf)\n1953", "x": -2040, "y": -1160, "width": 240, "height": 80, "color": "1"}, {"id": "af13f34b6f4b1008", "type": "text", "text": "Assembly languages ([Booth](file:///Users/<USER>/Literature/CS/Languages/assembly_1947.pdf))\nSep 1947", "x": -4200, "y": -1880, "width": 280, "height": 80, "color": "2"}, {"id": "7f2d3d7427b5e540", "type": "text", "text": "Machine languages\n1940s", "x": -4600, "y": -2040, "width": 240, "height": 80, "color": "5"}, {"id": "09ce9b41e225f417", "type": "text", "text": "A-2 ([part 1, pp. 25-29](file:///Users/<USER>/Literature/CS/Languages/computers_and_automation_1955.pdf)) ([part 2, pp. 15-27](file:///Users/<USER>/Literature/CS/Languages/computers_and_automation_1955_2.pdf))\n1953", "x": -2040, "y": -1400, "width": 240, "height": 80, "color": "#c240d4"}, {"id": "59bc0d19da6f728a", "type": "text", "text": "[Math-Matic](file:///Users/<USER>/Literature/CS/Languages/math-arith-matics_1957.pdf)\n1957", "x": -600, "y": -1400, "width": 240, "height": 80, "color": "#9536a3"}, {"id": "b255392eaa337186", "type": "text", "text": "IPL-I ([proposal?](file:///Users/<USER>/Literature/CS/Languages/ipl_1957.pdf))\n1956", "x": -960, "y": -800, "width": 240, "height": 80, "color": "3"}, {"id": "7497014be02740b4", "type": "text", "text": "IPL-II\n1957", "x": -600, "y": -800, "width": 240, "height": 80, "color": "3"}, {"id": "08cc8dff9a9718a7", "type": "text", "text": "[IPL-V](file:///Users/<USER>/Literature/CS/Languages/ipl5_1964.pdf)\n1958", "x": -240, "y": -800, "width": 240, "height": 80, "color": "3"}, {"id": "58c13a5a9c57e652", "type": "file", "file": "fortran.md", "x": -2840, "y": -960, "width": 400, "height": 400}, {"id": "760a46ac93569183", "type": "file", "file": "cobol.md", "x": -2405, "y": -960, "width": 405, "height": 400}, {"id": "ca526009d1d14d76", "type": "file", "file": "lisp.md", "x": -2840, "y": -520, "width": 400, "height": 400}, {"id": "880dd550992a13d1", "type": "file", "file": "snobol.md", "x": -2400, "y": -520, "width": 400, "height": 400}, {"id": "cf386d5dfaa3de81", "type": "file", "file": "apl.md", "x": -2840, "y": -80, "width": 400, "height": 400}], "edges": [{"id": "ee1b459c8169bebd", "fromNode": "753762f9de2b747c", "fromSide": "right", "toNode": "1938ac6a7704881c", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "c139ab160e4d9ea5", "fromNode": "1938ac6a7704881c", "fromSide": "right", "toNode": "3c914e72ce82d1d0", "toSide": "left", "color": "2"}, {"id": "1280d86b7c87c5b3", "fromNode": "3c914e72ce82d1d0", "fromSide": "right", "toNode": "92c6dcc6d8abda67", "toSide": "left", "color": "2"}, {"id": "968f7e1a028bde76", "fromNode": "92c6dcc6d8abda67", "fromSide": "right", "toNode": "25dac6dd45c12bef", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "a2a88717b66d13c6", "fromNode": "25dac6dd45c12bef", "fromSide": "right", "toNode": "197e395b9a44fbd2", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "32743c3dc466821e", "fromNode": "197e395b9a44fbd2", "fromSide": "right", "toNode": "9fcb62c270aac6f4", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "4c1d96bb408d457a", "fromNode": "9fcb62c270aac6f4", "fromSide": "right", "toNode": "394cd599c5d0fa03", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "339d71000da1f578", "fromNode": "394cd599c5d0fa03", "fromSide": "right", "toNode": "65141b302af3a257", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "e7e7c1f06e39d67b", "fromNode": "65141b302af3a257", "fromSide": "right", "toNode": "f12a1d1c24799b85", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "4461760d1df06ded", "fromNode": "f12a1d1c24799b85", "fromSide": "right", "toNode": "945e157b65280998", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "c7eb9c90df02a2e4", "fromNode": "945e157b65280998", "fromSide": "right", "toNode": "b2bde5d46a64d9c2", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "797edf734bcc904b", "fromNode": "b2bde5d46a64d9c2", "fromSide": "right", "toNode": "4ec63e2651c8f05e", "toSide": "left", "color": "2", "label": "FORTRAN"}, {"id": "92a904d866eac8a5", "fromNode": "fd4ac995fcf25a84", "fromSide": "right", "toNode": "2b85a5912c6b5890", "toSide": "left", "color": "#9536a3"}, {"id": "9b5347445df0da34", "fromNode": "2b85a5912c6b5890", "fromSide": "right", "toNode": "8868b97247b5867b", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "fec7fe5f24aa6a03", "fromNode": "8868b97247b5867b", "fromSide": "right", "toNode": "dfc2993b5696074e", "toSide": "left", "color": "1"}, {"id": "8a233d8703c678b5", "fromNode": "dfc2993b5696074e", "fromSide": "right", "toNode": "3c3d07ef80764f97", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "e11d33312898bfd6", "fromNode": "3c3d07ef80764f97", "fromSide": "right", "toNode": "0aa238523fd00501", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "c03bfb649485e579", "fromNode": "0aa238523fd00501", "fromSide": "right", "toNode": "0dd170c33f34956f", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "6f38055983407f87", "fromNode": "0dd170c33f34956f", "fromSide": "right", "toNode": "6ae2fbeb4ae24416", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "ce6a1612a246ab42", "fromNode": "6ae2fbeb4ae24416", "fromSide": "right", "toNode": "2aca7ca44f752cf0", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "fea950d6243da5ab", "fromNode": "2aca7ca44f752cf0", "fromSide": "right", "toNode": "89d4394cf3020b85", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "1bed414081b65bf4", "fromNode": "89d4394cf3020b85", "fromSide": "right", "toNode": "9c67e42943c1073d", "toSide": "left", "color": "1", "label": "COBOL"}, {"id": "9b0cf2e8ca1e6029", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "c0ebef496bbf60ff", "toSide": "left", "color": "5", "label": "ALGOL"}, {"id": "707c862baa8048af", "fromNode": "a9dbba8479ad4b1c", "fromSide": "right", "toNode": "6f595d87160f13b0", "toSide": "left", "color": "6"}, {"id": "c12f476ecb8a1924", "fromNode": "6f595d87160f13b0", "fromSide": "right", "toNode": "44360019663262fa", "toSide": "left", "color": "6", "label": "LISP"}, {"id": "e9d703b597cc2377", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "94776504e026acbe", "toSide": "left", "color": "6", "label": "LISP"}, {"id": "4310a01c0794e31c", "fromNode": "94776504e026acbe", "fromSide": "right", "toNode": "b03b6f32f4b029b9", "toSide": "left", "color": "6", "label": "LISP"}, {"id": "2f6552aee85e0ac4", "fromNode": "34d8fd541ccb3255", "fromSide": "right", "toNode": "981a0fe131abc6c3", "toSide": "left", "color": "3", "label": "APL"}, {"id": "12d0a28b446f8056", "fromNode": "981a0fe131abc6c3", "fromSide": "right", "toNode": "0e3495572794d8c9", "toSide": "left", "color": "3", "label": "APL"}, {"id": "3f81a7cf375e36c7", "fromNode": "c2135b11ad0c621a", "fromSide": "right", "toNode": "0d75bff66cf846be", "toSide": "left", "color": "4", "label": "SNOBOL"}, {"id": "d984cedb6ae4bede", "fromNode": "0d75bff66cf846be", "fromSide": "right", "toNode": "adc16d8d4aa31f0d", "toSide": "left", "color": "4"}, {"id": "f8e01a5520fa687c", "fromNode": "adc16d8d4aa31f0d", "fromSide": "right", "toNode": "b4e13a9149cf0bfb", "toSide": "left", "color": "4", "label": "SNOBOL"}, {"id": "8d49ed674b40f831", "fromNode": "7ebb0d6d8492645a", "fromSide": "right", "toNode": "c2135b11ad0c621a", "toSide": "left", "color": "#e01f99"}, {"id": "9c55d99cacab1bc9", "fromNode": "dfc2993b5696074e", "fromSide": "right", "toNode": "b4e13a9149cf0bfb", "toSide": "left", "color": "1"}, {"id": "9d0daa4b0e5b9121", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "34708de806194c2d", "toSide": "left", "color": "5"}, {"id": "4c0ba7fc3e13a08c", "fromNode": "34708de806194c2d", "fromSide": "right", "toNode": "715064fdd0c7c550", "toSide": "left", "color": "#b78a8a", "label": "(B)CPL"}, {"id": "df3f5beb72ddefa6", "fromNode": "715064fdd0c7c550", "fromSide": "right", "toNode": "0b962acecaabf739", "toSide": "left", "color": "#b78a8a", "label": "(B)CPL"}, {"id": "e4f0b9e9f834b1d5", "fromNode": "0b962acecaabf739", "fromSide": "right", "toNode": "e2bacfbf90e1d9ac", "toSide": "left", "color": "#b78a8a"}, {"id": "3434ffd48ab47b05", "fromNode": "e2bacfbf90e1d9ac", "fromSide": "right", "toNode": "bfd1b250699404d5", "toSide": "left", "color": "#28b525", "label": "C"}, {"id": "82e44d54f9bb550c", "fromNode": "bfd1b250699404d5", "fromSide": "right", "toNode": "94e7ade9b305e1b2", "toSide": "left", "color": "#29b525", "label": "C"}, {"id": "51ba9c7b67585c9f", "fromNode": "94e7ade9b305e1b2", "fromSide": "right", "toNode": "fbf8bae5652ec6af", "toSide": "left", "color": "#29b525", "label": "C"}, {"id": "8e2582599c0debba", "fromNode": "fbf8bae5652ec6af", "fromSide": "right", "toNode": "75b8fe9096fc6d4f", "toSide": "left", "color": "#29b525", "label": "C"}, {"id": "bab183bbeac6e865", "fromNode": "75b8fe9096fc6d4f", "fromSide": "right", "toNode": "ac32fb730ac16b9e", "toSide": "left", "color": "#29b525", "label": "C"}, {"id": "afd902a01c5cc097", "fromNode": "1aed8b4b4449ccee", "fromSide": "right", "toNode": "6b7de1bb2f01ec62", "toSide": "left", "color": "#29b525", "label": "C"}, {"id": "39a523e500d7561e", "fromNode": "ac32fb730ac16b9e", "fromSide": "right", "toNode": "1aed8b4b4449ccee", "toSide": "left", "color": "#29b525", "label": "C"}, {"id": "db75563c2302a685", "fromNode": "c0ebef496bbf60ff", "fromSide": "right", "toNode": "e2bacfbf90e1d9ac", "toSide": "left", "color": "5"}, {"id": "a9bc1962b1a15853", "fromNode": "3c914e72ce82d1d0", "fromSide": "right", "toNode": "de1b0df301cc15f8", "toSide": "left", "color": "2"}, {"id": "76fa1da16b51cc63", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "de1b0df301cc15f8", "toSide": "left", "color": "5"}, {"id": "ccc2d479298255a1", "fromNode": "de1b0df301cc15f8", "fromSide": "right", "toNode": "96bb9f881924de3f", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "bfba680fe39a5457", "fromNode": "96bb9f881924de3f", "fromSide": "right", "toNode": "067d433ea664c147", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "5106693681207a9f", "fromNode": "067d433ea664c147", "fromSide": "right", "toNode": "45047f152878c2f1", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "a03f968ccbaf4c47", "fromNode": "45047f152878c2f1", "fromSide": "right", "toNode": "f4dbd9f41c5a4e3f", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "1255bf76f9061fd6", "fromNode": "f4dbd9f41c5a4e3f", "fromSide": "right", "toNode": "e8b0f0a27d68ae17", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "efef071a41a50807", "fromNode": "e8b0f0a27d68ae17", "fromSide": "right", "toNode": "87b734ef5cf2cfe1", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "70048f8cc2b419da", "fromNode": "87b734ef5cf2cfe1", "fromSide": "right", "toNode": "ba7f0fa40442eb0c", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "429eb55b31a5af1b", "fromNode": "ba7f0fa40442eb0c", "fromSide": "right", "toNode": "c8393c3eaa55fffd", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "6f894f1c3ef0c046", "fromNode": "c8393c3eaa55fffd", "fromSide": "right", "toNode": "9861d02642f9fcd5", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "21573986875b4c41", "fromNode": "9861d02642f9fcd5", "fromSide": "right", "toNode": "d366bf1c7b6e4ecc", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "2fd0c62c6164a341", "fromNode": "d366bf1c7b6e4ecc", "fromSide": "right", "toNode": "d1299e6c3399a8bb", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "f6bbbb806c657b3e", "fromNode": "d1299e6c3399a8bb", "fromSide": "right", "toNode": "ff635debb93e8f49", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "e122b7b11ac6fde1", "fromNode": "ff635debb93e8f49", "fromSide": "right", "toNode": "c651e5700bbafbfc", "toSide": "left", "color": "#ec88d7", "label": "BASIC"}, {"id": "029d4a349b01ecb7", "fromNode": "f147f371c149bf7d", "fromSide": "right", "toNode": "0b3712f3368ea93c", "toSide": "left", "color": "#d14f2e", "label": "PL/I"}, {"id": "5ccfa12227f41909", "fromNode": "47ed4df1a89a7e41", "fromSide": "right", "toNode": "8b4c1fe1ab07f152", "toSide": "left", "color": "#719d20", "label": "<PERSON><PERSON><PERSON>"}, {"id": "2612296bad301005", "fromNode": "3c914e72ce82d1d0", "fromSide": "right", "toNode": "f147f371c149bf7d", "toSide": "left", "color": "2"}, {"id": "94b1816e6c0e2b9e", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "f147f371c149bf7d", "toSide": "left", "color": "5"}, {"id": "cf29e190433f31fd", "fromNode": "dfc2993b5696074e", "fromSide": "right", "toNode": "f147f371c149bf7d", "toSide": "left", "color": "1"}, {"id": "bc2cdd3094c08c45", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "47ed4df1a89a7e41", "toSide": "left", "color": "5"}, {"id": "9daf8ff5188bce67", "fromNode": "6f595d87160f13b0", "fromSide": "right", "toNode": "6791f4000efd2b0f", "toSide": "left", "color": "6"}, {"id": "cfd8d1b55fde338b", "fromNode": "3ac413fc7120245e", "fromSide": "right", "toNode": "a190bd83eccfac47", "toSide": "left", "color": "#00706e", "label": "Smalltalk"}, {"id": "3abeb2148b5fe825", "fromNode": "a190bd83eccfac47", "fromSide": "right", "toNode": "10d372b053c49b92", "toSide": "left", "color": "#00706e", "label": "Smalltalk"}, {"id": "a19191a86da8ea87", "fromNode": "10d372b053c49b92", "fromSide": "right", "toNode": "a18b062351021b4e", "toSide": "left", "color": "#00706e", "label": "Smalltalk"}, {"id": "ee4c56c6c6a1ca73", "fromNode": "a18b062351021b4e", "fromSide": "right", "toNode": "93f593092c9707b4", "toSide": "left", "color": "#00706e", "label": "Smalltalk"}, {"id": "98168086e94b109d", "fromNode": "93f593092c9707b4", "fromSide": "right", "toNode": "7d753ded1a1f1d46", "toSide": "left", "color": "#00706e", "label": "Smalltalk"}, {"id": "ae88ba6ca0e49ce5", "fromNode": "8b4c1fe1ab07f152", "fromSide": "right", "toNode": "3ac413fc7120245e", "toSide": "left", "color": "#719d20"}, {"id": "a92dffe89e807e1d", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "3ac413fc7120245e", "toSide": "left", "color": "6"}, {"id": "759d4d0c0ace86b3", "fromNode": "a182d7c3d700beae", "fromSide": "right", "toNode": "87e305d349c19607", "toSide": "left", "color": "#7856c8", "label": "Forth"}, {"id": "b240448f50ab3708", "fromNode": "48e446b49758b138", "fromSide": "right", "toNode": "850c224ff2021d17", "toSide": "left", "color": "#92c256", "label": "Prolog"}, {"id": "a2ab4b0914f5a264", "fromNode": "4aaa9ccc6b68de52", "fromSide": "right", "toNode": "48e446b49758b138", "toSide": "left", "color": "#92c256", "label": "Prolog"}, {"id": "690e4bda25f863e4", "fromNode": "7b053f632b22f37b", "fromSide": "right", "toNode": "4aaa9ccc6b68de52", "toSide": "left", "color": "#92c256", "label": "Prolog"}, {"id": "b27ac87f0ed9a496", "fromNode": "02f1c60f274abf39", "fromSide": "right", "toNode": "7b053f632b22f37b", "toSide": "left", "color": "1"}, {"id": "d37ad0ef230c3666", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "c2dce253fcac2fbd", "toSide": "left", "color": "5"}, {"id": "0a10e6203e20e138", "fromNode": "c2dce253fcac2fbd", "fromSide": "right", "toNode": "07ea68796b915cb6", "toSide": "left", "color": "1"}, {"id": "59f8db9c4627d642", "fromNode": "8b4c1fe1ab07f152", "fromSide": "right", "toNode": "07ea68796b915cb6", "toSide": "left", "color": "#719d20"}, {"id": "f5776231e266ddba", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "88b9dcaffabe4cad", "toSide": "left", "color": "5"}, {"id": "4515048249d41d87", "fromNode": "efa1dcdec2848641", "fromSide": "right", "toNode": "a4941f76b6a4712b", "toSide": "left", "color": "#1d6d35", "label": "<PERSON><PERSON><PERSON>"}, {"id": "4560944e6d572376", "fromNode": "a4941f76b6a4712b", "fromSide": "right", "toNode": "738bbf3eee22378d", "toSide": "left", "color": "#1d6d35", "label": "<PERSON><PERSON><PERSON>"}, {"id": "000ee1c46fc72170", "fromNode": "07ea68796b915cb6", "fromSide": "right", "toNode": "efa1dcdec2848641", "toSide": "left", "color": "#e9973f"}, {"id": "fc5f496571bcab11", "fromNode": "25e14ceab2b9bd6d", "fromSide": "right", "toNode": "f995802ac67a2293", "toSide": "left", "color": "#c26f0f", "label": "Scheme"}, {"id": "a62cf70b53a624f4", "fromNode": "5635c7fc9f4e5b10", "fromSide": "right", "toNode": "362ef24557d99fc6", "toSide": "left", "color": "#c26f0f", "label": "Scheme"}, {"id": "6af2147da9dfa19b", "fromNode": "362ef24557d99fc6", "fromSide": "right", "toNode": "b3fe3e2eb4e12c01", "toSide": "left", "color": "#c26f0f", "label": "Scheme"}, {"id": "3024f98f60d3cb80", "fromNode": "f995802ac67a2293", "fromSide": "right", "toNode": "5635c7fc9f4e5b10", "toSide": "left", "color": "#c26f0f", "label": "Scheme"}, {"id": "adf010022ea9d62f", "fromNode": "eee718fa526d7f6b", "fromSide": "right", "toNode": "84f5164583901577", "toSide": "left", "color": "#c26f0f", "label": "Scheme"}, {"id": "6999700a144ebbe4", "fromNode": "b3fe3e2eb4e12c01", "fromSide": "right", "toNode": "eee718fa526d7f6b", "toSide": "left", "color": "#c26f0f", "label": "Scheme"}, {"id": "d9464e8c3a046b3c", "fromNode": "d70d8e78ceb3e694", "fromSide": "right", "toNode": "25e14ceab2b9bd6d", "toSide": "left", "color": "5"}, {"id": "c9a6c219006a02db", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "25e14ceab2b9bd6d", "toSide": "left", "color": "6"}, {"id": "097c542c74c54c91", "fromNode": "07ea68796b915cb6", "fromSide": "right", "toNode": "2af38e931b72706b", "toSide": "left", "color": "#e9973f"}, {"id": "7f822e6252829720", "fromNode": "c0ebef496bbf60ff", "fromSide": "right", "toNode": "2af38e931b72706b", "toSide": "left", "color": "5"}, {"id": "a66ab972dfeb23c1", "fromNode": "666130658c516db9", "fromSide": "right", "toNode": "c0713e100b988837", "toSide": "left", "color": "#c85b60", "label": "(n)awk"}, {"id": "1f7d2d9c3d21e704", "fromNode": "e2bacfbf90e1d9ac", "fromSide": "right", "toNode": "666130658c516db9", "toSide": "left", "color": "#26ec22"}, {"id": "548244ef12a103a8", "fromNode": "6791f4000efd2b0f", "fromSide": "right", "toNode": "e553d4d95a0ca4d6", "toSide": "left", "color": "#5f8bf2"}, {"id": "672f37fbe7426347", "fromNode": "6791f4000efd2b0f", "fromSide": "right", "toNode": "94cff715bbee4cde", "toSide": "left", "color": "#5f8bf2"}, {"id": "bb6a1cad3f4ef647", "fromNode": "94cff715bbee4cde", "fromSide": "right", "toNode": "202776b8ca29173a", "toSide": "left", "color": "#8d2020", "label": "(S)ML"}, {"id": "162b7d6c58117065", "fromNode": "2571b01b629c7026", "fromSide": "right", "toNode": "4c7afb464acb3a79", "toSide": "left", "color": "#8d2020", "label": "(S)ML"}, {"id": "ae601ae953293e98", "fromNode": "202776b8ca29173a", "fromSide": "right", "toNode": "2571b01b629c7026", "toSide": "left", "color": "#8d2020", "label": "(S)ML"}, {"id": "af0cdaeee4a88178", "fromNode": "a88aeaf74090ef52", "fromSide": "right", "toNode": "419c93edd0eb2437", "toSide": "left", "color": "#dd3c8a", "label": "Ada"}, {"id": "b199ce0ae568acf4", "fromNode": "419c93edd0eb2437", "fromSide": "right", "toNode": "60cd79ceb384da34", "toSide": "left", "color": "#dd3c8a", "label": "Ada"}, {"id": "d11ac9f87873e974", "fromNode": "60cd79ceb384da34", "fromSide": "right", "toNode": "994ad289ecf416b0", "toSide": "left", "color": "#dd3c8a", "label": "Ada"}, {"id": "e9ff3997284ce063", "fromNode": "22376900bc0111fa", "fromSide": "right", "toNode": "afa369bd9303dd4e", "toSide": "left", "color": "#dd3c8a", "label": "Ada"}, {"id": "12725dbd0842545e", "fromNode": "2bd93694b08d6313", "fromSide": "right", "toNode": "22376900bc0111fa", "toSide": "left", "color": "#dd3c8a", "label": "Ada"}, {"id": "f20d4d9d0cc44394", "fromNode": "994ad289ecf416b0", "fromSide": "right", "toNode": "2bd93694b08d6313", "toSide": "left", "color": "#dd3c8a", "label": "Ada"}, {"id": "fc0a146ee5bd390e", "fromNode": "07ea68796b915cb6", "fromSide": "right", "toNode": "a88aeaf74090ef52", "toSide": "left", "color": "#e9973f"}, {"id": "8d452e8dc8bc109d", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "2aca7ca44f752cf0", "toSide": "left", "color": "#17706e"}, {"id": "4c7478e987816fbd", "fromNode": "925a4b05e753b3ed", "fromSide": "right", "toNode": "10a80a99157f6713", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "2355f76f730b1c9a", "fromNode": "10a80a99157f6713", "fromSide": "right", "toNode": "e1081daf0da03ba9", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "a77102991937a5e3", "fromNode": "a6b53cae6b969341", "fromSide": "right", "toNode": "925a4b05e753b3ed", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "ccedcac4ddc56cd3", "fromNode": "81f5eb2ff5096a84", "fromSide": "right", "toNode": "a6b53cae6b969341", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "5a92e124e12627c1", "fromNode": "52ea50cd3f991426", "fromSide": "right", "toNode": "81f5eb2ff5096a84", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "f43209d7e3ed0ac4", "fromNode": "1480d0eeeda1a7ec", "fromSide": "right", "toNode": "52ea50cd3f991426", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "666ce413a795245d", "fromNode": "2cf8964ff0fe18ee", "fromSide": "right", "toNode": "1480d0eeeda1a7ec", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "beb98062d9ec1f14", "fromNode": "8463660da76a9feb", "fromSide": "right", "toNode": "2cf8964ff0fe18ee", "toSide": "left", "color": "#add02f", "label": "C++"}, {"id": "9faa7ed8b3408b83", "fromNode": "bfd1b250699404d5", "fromSide": "right", "toNode": "8463660da76a9feb", "toSide": "left", "color": "#29b525"}, {"id": "ead955da5dfec6c3", "fromNode": "8b4c1fe1ab07f152", "fromSide": "right", "toNode": "8463660da76a9feb", "toSide": "left", "color": "#719d20"}, {"id": "e4764567fe4a9d41", "fromNode": "94e7ade9b305e1b2", "fromSide": "right", "toNode": "1480d0eeeda1a7ec", "toSide": "left", "color": "#29b525"}, {"id": "1a32c35a43bb86fe", "fromNode": "75b8fe9096fc6d4f", "fromSide": "right", "toNode": "81f5eb2ff5096a84", "toSide": "left", "color": "#29b525"}, {"id": "a01a25f5651ad524", "fromNode": "e553d4d95a0ca4d6", "fromSide": "right", "toNode": "527e37f058438176", "toSide": "left", "color": "5"}, {"id": "86317298c911a405", "fromNode": "d2259e6be4fdedc9", "fromSide": "right", "toNode": "4da7574c2ad794de", "toSide": "left", "color": "#ab2b31", "label": "ABC"}, {"id": "b926808d611e9d49", "fromNode": "2af38e931b72706b", "fromSide": "right", "toNode": "a4941f76b6a4712b", "toSide": "left", "color": "#555b06"}, {"id": "ddac6f19da960439", "fromNode": "5c4eb58efba1ee96", "fromSide": "right", "toNode": "342cd13d3be962b7", "toSide": "left", "color": "3", "label": "Postscript"}, {"id": "563e218b544e8df5", "fromNode": "342cd13d3be962b7", "fromSide": "right", "toNode": "d86403b3af508d01", "toSide": "left", "color": "3", "label": "Postscript"}, {"id": "9752fde55227853d", "fromNode": "a182d7c3d700beae", "fromSide": "right", "toNode": "5c4eb58efba1ee96", "toSide": "left", "color": "#7856c8"}, {"id": "3131da4450d2880a", "fromNode": "527e37f058438176", "fromSide": "right", "toNode": "e39931030afc50e0", "toSide": "left", "color": "1"}, {"id": "5c314602a16ac818", "fromNode": "e553d4d95a0ca4d6", "fromSide": "right", "toNode": "e39931030afc50e0", "toSide": "left", "color": "5"}, {"id": "e2094fb5f4894e9f", "fromNode": "bfd1b250699404d5", "fromSide": "right", "toNode": "0db6648d3f3bd36a", "toSide": "left", "color": "#29b525"}, {"id": "036384a6a2c6710c", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "0db6648d3f3bd36a", "toSide": "left", "color": "#17706e"}, {"id": "99e6f8f2331b9feb", "fromNode": "2af38e931b72706b", "fromSide": "right", "toNode": "4b4788f11a97449b", "toSide": "left", "color": "#555b06"}, {"id": "06f40c8f3a37ea10", "fromNode": "243367b7f0f47032", "fromSide": "right", "toNode": "995a16254724d645", "toSide": "left", "color": "#7934b2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "3e027c5f809eff54", "fromNode": "995a16254724d645", "fromSide": "right", "toNode": "9b106a680f5f8d1b", "toSide": "left", "color": "#7934b2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "f8567f8f541b311e", "fromNode": "9b106a680f5f8d1b", "fromSide": "right", "toNode": "799b759dbb59ca4e", "toSide": "left", "color": "#7934b2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "e109561b6a40b4e7", "fromNode": "799b759dbb59ca4e", "fromSide": "right", "toNode": "5073e7de6d81bd69", "toSide": "left", "color": "#7934b2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "90aa38f21934ddda", "fromNode": "5073e7de6d81bd69", "fromSide": "right", "toNode": "24790e5e73231b5c", "toSide": "left", "color": "#7934b2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "c9eb2df02388eaeb", "fromNode": "24790e5e73231b5c", "fromSide": "right", "toNode": "c6f4c4c8010b6980", "toSide": "left", "color": "#7934b2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "f82ac03421b25b8d", "fromNode": "419c93edd0eb2437", "fromSide": "right", "toNode": "243367b7f0f47032", "toSide": "left", "color": "#dd3c8a"}, {"id": "0e4ec4bd02febc72", "fromNode": "8b4c1fe1ab07f152", "fromSide": "right", "toNode": "243367b7f0f47032", "toSide": "left", "color": "#719d20"}, {"id": "d08bbad24b1b554b", "fromNode": "2969730f9e6c65c9", "fromSide": "right", "toNode": "29276080bdaf1df1", "toSide": "left", "color": "4", "label": "Oberon"}, {"id": "4c78476bc9310c41", "fromNode": "a4941f76b6a4712b", "fromSide": "right", "toNode": "2969730f9e6c65c9", "toSide": "left", "color": "#1d6d35"}, {"id": "15709a99dcdfaf6d", "fromNode": "55dfa8fec3d81748", "fromSide": "right", "toNode": "ce3c1078228a3616", "toSide": "left", "color": "3", "label": "<PERSON><PERSON>"}, {"id": "62ad54f1f5facb72", "fromNode": "ce3c1078228a3616", "fromSide": "right", "toNode": "553557c54a290714", "toSide": "left", "color": "3", "label": "<PERSON><PERSON>"}, {"id": "e602e6a3f9114ddf", "fromNode": "553557c54a290714", "fromSide": "right", "toNode": "f53d6f6224ce3a3e", "toSide": "left", "color": "3", "label": "<PERSON><PERSON>"}, {"id": "2e8eb07e56513f0b", "fromNode": "f53d6f6224ce3a3e", "fromSide": "right", "toNode": "d623f2b066846c0b", "toSide": "left", "color": "3", "label": "<PERSON><PERSON>"}, {"id": "99d0cc51d1db145f", "fromNode": "d623f2b066846c0b", "fromSide": "right", "toNode": "256ce0855a746621", "toSide": "left", "color": "3", "label": "<PERSON><PERSON>"}, {"id": "efb942b26834d1ab", "fromNode": "666130658c516db9", "fromSide": "right", "toNode": "55dfa8fec3d81748", "toSide": "left", "color": "#c85b60"}, {"id": "74c25d782758fd7c", "fromNode": "ba07fc0ecd1be720", "fromSide": "right", "toNode": "172e820c8abce4e1", "toSide": "left", "color": "#da6df8"}, {"id": "72261241910ddb2d", "fromNode": "e2bacfbf90e1d9ac", "fromSide": "right", "toNode": "172e820c8abce4e1", "toSide": "left", "color": "#26ec22"}, {"id": "d9554df99ebc251a", "fromNode": "94cff715bbee4cde", "fromSide": "right", "toNode": "e39931030afc50e0", "toSide": "left", "color": "#8d2020"}, {"id": "e5737748d4919d67", "fromNode": "172e820c8abce4e1", "fromSide": "right", "toNode": "33cd5f5df1073e3a", "toSide": "left", "color": "3", "label": "(t)csh"}, {"id": "397142b17e2e59c0", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "45ac3b29f492e36d", "toSide": "left", "color": "#17706e"}, {"id": "c772bb44c4982f3d", "fromNode": "45ac3b29f492e36d", "fromSide": "right", "toNode": "28fa63a0fa57c75f", "toSide": "left", "color": "1", "label": "Self"}, {"id": "d2fce250ab7f1e22", "fromNode": "28fa63a0fa57c75f", "fromSide": "right", "toNode": "df0095ab823b02ea", "toSide": "left", "color": "1", "label": "Self\n"}, {"id": "e3d4fe89b01c2562", "fromNode": "df0095ab823b02ea", "fromSide": "right", "toNode": "d16fb48c73214020", "toSide": "left", "color": "1", "label": "Self\n"}, {"id": "765c3a0c11d69d11", "fromNode": "d16fb48c73214020", "fromSide": "right", "toNode": "1927f05d86eeb498", "toSide": "left", "color": "1", "label": "Self\n"}, {"id": "15ecde062945a05a", "fromNode": "1927f05d86eeb498", "fromSide": "right", "toNode": "60c1c84a120b1557", "toSide": "left", "color": "1", "label": "Self\n"}, {"id": "9a38084701df7498", "fromNode": "60c1c84a120b1557", "fromSide": "right", "toNode": "2b2e4795ea7d5523", "toSide": "left", "color": "1", "label": "Self\n"}, {"id": "5c6a1b3c1798076f", "fromNode": "2b2e4795ea7d5523", "fromSide": "right", "toNode": "f9483004ffd6b764", "toSide": "left", "color": "1", "label": "Self\n"}, {"id": "529442af2ee25488", "fromNode": "c383751f1d8ec6c7", "fromSide": "right", "toNode": "0b7df5009c3650c2", "toSide": "left", "color": "5", "label": "perl"}, {"id": "75f3bb598dac9f6a", "fromNode": "0b7df5009c3650c2", "fromSide": "right", "toNode": "4336571ec0523152", "toSide": "left", "color": "5", "label": "perl"}, {"id": "f8e685bd05cb75f9", "fromNode": "4336571ec0523152", "fromSide": "right", "toNode": "156d467a3d1592e3", "toSide": "left", "color": "5", "label": "perl"}, {"id": "accf4b20d3f86a26", "fromNode": "156d467a3d1592e3", "fromSide": "right", "toNode": "4066a5702794d7fa", "toSide": "left", "color": "5", "label": "perl"}, {"id": "ba4d178286cd5217", "fromNode": "4066a5702794d7fa", "fromSide": "right", "toNode": "0bfba93dbd23eda1", "toSide": "left", "color": "5", "label": "perl"}, {"id": "1db5e36878b210aa", "fromNode": "94061c92ba1e27ee", "fromSide": "right", "toNode": "fce5b4cf7b45084b", "toSide": "left", "color": "5", "label": "perl"}, {"id": "d16bcd61cb889429", "fromNode": "fce5b4cf7b45084b", "fromSide": "right", "toNode": "2cc2ec170ebf8907", "toSide": "left", "color": "5", "label": "perl"}, {"id": "a816a786c1e4dd0a", "fromNode": "2cc2ec170ebf8907", "fromSide": "right", "toNode": "33467aecc7112eca", "toSide": "left", "color": "5", "label": "perl"}, {"id": "08104b360d6ab69c", "fromNode": "33467aecc7112eca", "fromSide": "right", "toNode": "2160fd7562c2f7f7", "toSide": "left", "color": "5", "label": "perl"}, {"id": "097a72eed6ea1d96", "fromNode": "2160fd7562c2f7f7", "fromSide": "right", "toNode": "2e0ab9bc40028208", "toSide": "left", "color": "5", "label": "perl"}, {"id": "7daea6c8e05f4d2a", "fromNode": "2e0ab9bc40028208", "fromSide": "right", "toNode": "932906ddb812db40", "toSide": "left", "color": "5", "label": "perl"}, {"id": "a1d1ec4c7e0fd506", "fromNode": "932906ddb812db40", "fromSide": "right", "toNode": "bd2ba61f7f82055b", "toSide": "left", "color": "5", "label": "perl"}, {"id": "09fa688d1ad0fa60", "fromNode": "bd2ba61f7f82055b", "fromSide": "right", "toNode": "3207a448a211fc53", "toSide": "left", "color": "5", "label": "perl"}, {"id": "d7604ae1e2058e72", "fromNode": "3207a448a211fc53", "fromSide": "right", "toNode": "7826b2bc0738ed1c", "toSide": "left", "color": "5", "label": "perl"}, {"id": "9a43ea35c6e085ee", "fromNode": "c0713e100b988837", "fromSide": "right", "toNode": "c383751f1d8ec6c7", "toSide": "left", "color": "#c85b60"}, {"id": "122af2d42acba74d", "fromNode": "ba07fc0ecd1be720", "fromSide": "right", "toNode": "c383751f1d8ec6c7", "toSide": "left", "color": "#da6df8"}, {"id": "faf98da13485557b", "fromNode": "52f8df88ab92656b", "fromSide": "right", "toNode": "b0c96a36af3dc828", "toSide": "left", "color": "#965494", "label": "<PERSON><PERSON>"}, {"id": "278b6b15ee23e054", "fromNode": "b0c96a36af3dc828", "fromSide": "right", "toNode": "734ebb83ee303518", "toSide": "left", "color": "#965494", "label": "<PERSON><PERSON>\n"}, {"id": "56e304929d7cf4da", "fromNode": "734ebb83ee303518", "fromSide": "right", "toNode": "614c3a8d82bf09db", "toSide": "left", "color": "#965494", "label": "<PERSON><PERSON>\n"}, {"id": "cde86cf3b7910135", "fromNode": "614c3a8d82bf09db", "fromSide": "right", "toNode": "f5fcd082502ca8c3", "toSide": "left", "color": "#965494"}, {"id": "bfb1aa0218efd011", "fromNode": "f5fcd082502ca8c3", "fromSide": "right", "toNode": "0c09a058cf468575", "toSide": "left", "color": "#965494", "label": "<PERSON><PERSON>\n"}, {"id": "20ae7e13a4156c55", "fromNode": "0c09a058cf468575", "fromSide": "right", "toNode": "b5b9f53f4e419f40", "toSide": "left", "color": "#965494", "label": "<PERSON><PERSON>\n"}, {"id": "5bbe19f38acdfc46", "fromNode": "e39931030afc50e0", "fromSide": "right", "toNode": "52f8df88ab92656b", "toSide": "left", "color": "5"}, {"id": "f9c71bd6a130c0cf", "fromNode": "b5ebea958daea3b0", "fromSide": "right", "toNode": "1fded19e363d6852", "toSide": "left", "color": "4", "label": "<PERSON><PERSON>"}, {"id": "d356f1e1e6d2973f", "fromNode": "202776b8ca29173a", "fromSide": "right", "toNode": "b5ebea958daea3b0", "toSide": "left", "color": "#8d2020"}, {"id": "b8a2ef5004707cea", "fromNode": "1fded19e363d6852", "fromSide": "right", "toNode": "54795c07ddd90b14", "toSide": "left", "color": "4", "label": "<PERSON><PERSON>\n"}, {"id": "74861181aa799582", "fromNode": "31e228ae0869ab23", "fromSide": "right", "toNode": "02638b7db2030656", "toSide": "left", "color": "#5233f0", "label": "Tcl"}, {"id": "812940aab5bd6f3e", "fromNode": "45df154795344164", "fromSide": "right", "toNode": "f9ac07081316f7b6", "toSide": "left", "color": "#5233f0", "label": "Tcl\n"}, {"id": "2a0c224709cac042", "fromNode": "f9ac07081316f7b6", "fromSide": "right", "toNode": "3c1f4ba53f9a2f71", "toSide": "left", "color": "#5233f0", "label": "Tcl\n"}, {"id": "74d8f53351a7b474", "fromNode": "3c1f4ba53f9a2f71", "fromSide": "right", "toNode": "2350c190fa97a3f4", "toSide": "left", "color": "#5233f0", "label": "Tcl\n"}, {"id": "c61af370f80a06af", "fromNode": "2350c190fa97a3f4", "fromSide": "right", "toNode": "08383046ebf8cb87", "toSide": "left", "color": "#5233f0", "label": "Tcl\n"}, {"id": "6407d58c26a77e31", "fromNode": "08383046ebf8cb87", "fromSide": "right", "toNode": "31e79e3e4f703bdf", "toSide": "left", "color": "#5233f0", "label": "Tcl\n"}, {"id": "fee2217aae782fdc", "fromNode": "5895538a36bd7663", "fromSide": "right", "toNode": "d0b362ec28377aeb", "toSide": "left", "color": "4", "label": "J"}, {"id": "5877a98ec1607a69", "fromNode": "d0b362ec28377aeb", "fromSide": "right", "toNode": "48dbb9ed81fec653", "toSide": "left", "color": "4", "label": "J"}, {"id": "a85dccaf55b66dab", "fromNode": "f2e5202fd6798462", "fromSide": "right", "toNode": "b16637761ff308c5", "toSide": "left", "color": "3", "label": "A"}, {"id": "a120dd039ab890e3", "fromNode": "5895538a36bd7663", "fromSide": "right", "toNode": "f2e5202fd6798462", "toSide": "left", "color": "4"}, {"id": "47675632791bd8e3", "fromNode": "a4941f76b6a4712b", "fromSide": "right", "toNode": "368e18cd77f9311a", "toSide": "left", "color": "#1d6d35"}, {"id": "9e7044acc0da07b2", "fromNode": "ba07fc0ecd1be720", "fromSide": "right", "toNode": "10185c905868b606", "toSide": "left", "color": "#da6df8"}, {"id": "03caeb461e8105a1", "fromNode": "52f8df88ab92656b", "fromSide": "right", "toNode": "37d4e4fa8e222d85", "toSide": "left", "color": "#965494"}, {"id": "e2f518981ce2d44c", "fromNode": "52f8df88ab92656b", "fromSide": "right", "toNode": "368e18cd77f9311a", "toSide": "left", "color": "#965494"}, {"id": "62f932ce760debd0", "fromNode": "94776504e026acbe", "fromSide": "right", "toNode": "37d4e4fa8e222d85", "toSide": "left", "color": "6"}, {"id": "e13c06f79a5576dc", "fromNode": "9a3fb98a45c97e6d", "fromSide": "right", "toNode": "0bb681d79cac75f6", "toSide": "left", "color": "#c63a0c", "label": "Python"}, {"id": "87334cdb258032b9", "fromNode": "0bb681d79cac75f6", "fromSide": "right", "toNode": "9405c4865055a2c0", "toSide": "left", "color": "#c63a0c", "label": "Python\n"}, {"id": "f30296a83dce1231", "fromNode": "9405c4865055a2c0", "fromSide": "right", "toNode": "56e189cc1529d962", "toSide": "left", "color": "#c63a0c"}, {"id": "c23742ea21a74b32", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "b0587dbbe19a64a9", "toSide": "left", "color": "#c63a0c", "label": "Python\n"}, {"id": "87b443e61da9c5b4", "fromNode": "94e7ade9b305e1b2", "fromSide": "right", "toNode": "9a3fb98a45c97e6d", "toSide": "left", "color": "#29b525"}, {"id": "13e4655f35cd70bd", "fromNode": "368e18cd77f9311a", "fromSide": "right", "toNode": "9a3fb98a45c97e6d", "toSide": "left", "color": "1"}, {"id": "a6108cadaf577222", "fromNode": "adeecbfd5706a4fd", "fromSide": "right", "toNode": "58ccc548eb1b71e2", "toSide": "left", "color": "3"}, {"id": "b300876307467930", "fromNode": "f53d6f6224ce3a3e", "fromSide": "right", "toNode": "adeecbfd5706a4fd", "toSide": "left", "color": "3"}, {"id": "4ff934203aa37c41", "fromNode": "34d8fd541ccb3255", "fromSide": "right", "toNode": "5895538a36bd7663", "toSide": "left", "color": "3"}, {"id": "2e1d3e76d8a17ab5", "fromNode": "ea9ec3da93fbe55f", "fromSide": "right", "toNode": "58ccc548eb1b71e2", "toSide": "left", "color": "2"}, {"id": "3af1a23e4b5079d8", "fromNode": "ea9ec3da93fbe55f", "fromSide": "right", "toNode": "ac54db3b9e631695", "toSide": "left", "color": "2", "label": "Java"}, {"id": "6ca7c47b8c12e7e3", "fromNode": "ac54db3b9e631695", "fromSide": "right", "toNode": "3896d32d8f25e590", "toSide": "left", "color": "2", "label": "Java"}, {"id": "8a143e908c5fbaa7", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "6f271025b6514abb", "toSide": "left", "color": "2", "label": "Java"}, {"id": "cc1e21e921e74eec", "fromNode": "6f271025b6514abb", "fromSide": "right", "toNode": "890f5ce014673fe5", "toSide": "left", "color": "2", "label": "Java"}, {"id": "9c96c9bc0f373c72", "fromNode": "890f5ce014673fe5", "fromSide": "right", "toNode": "64389e58a561f13b", "toSide": "left", "color": "2", "label": "Java\n"}, {"id": "24b8204036fd3c01", "fromNode": "64389e58a561f13b", "fromSide": "right", "toNode": "9201b7669aad7ed8", "toSide": "left", "color": "2"}, {"id": "6f09034ff1403698", "fromNode": "9201b7669aad7ed8", "fromSide": "right", "toNode": "299fe65c31b09983", "toSide": "left", "color": "2", "label": "Java"}, {"id": "cf34c072e7bef646", "fromNode": "299fe65c31b09983", "fromSide": "right", "toNode": "7ab7b0d79a8e7233", "toSide": "left", "color": "2", "label": "Java"}, {"id": "a8f4ee1929ae3f13", "fromNode": "419c93edd0eb2437", "fromSide": "right", "toNode": "ea9ec3da93fbe55f", "toSide": "left", "color": "#dd3c8a"}, {"id": "b8dadce5feea6606", "fromNode": "0db6648d3f3bd36a", "fromSide": "right", "toNode": "ea9ec3da93fbe55f", "toSide": "left", "color": "#ea6e39"}, {"id": "de9219832a8d7909", "fromNode": "2cf8964ff0fe18ee", "fromSide": "right", "toNode": "ea9ec3da93fbe55f", "toSide": "left", "color": "#add02f"}, {"id": "0183d2ad04800531", "fromNode": "4b4788f11a97449b", "fromSide": "right", "toNode": "ea9ec3da93fbe55f", "toSide": "left", "color": "#eb2ddc"}, {"id": "f88e8df32eac7308", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "ea9ec3da93fbe55f", "toSide": "left", "color": "#17706e"}, {"id": "4e697343446609ff", "fromNode": "5635c7fc9f4e5b10", "fromSide": "right", "toNode": "ea9ec3da93fbe55f", "toSide": "left", "color": "#c26f0f"}, {"id": "c3dc9e2e43ed8ed2", "fromNode": "37d4e4fa8e222d85", "fromSide": "right", "toNode": "19a4291aefd0323b", "toSide": "left", "color": "6"}, {"id": "bb3290079c67c623", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "19a4291aefd0323b", "toSide": "left", "color": "#17706e"}, {"id": "4e82d669b30bb663", "fromNode": "e1a80fd1bb37389e", "fromSide": "right", "toNode": "d120440c3d5019c9", "toSide": "left", "color": "3", "label": "JavaScript"}, {"id": "7512736bf61c61a0", "fromNode": "53bfc36400609496", "fromSide": "right", "toNode": "5bffc1471db5acc5", "toSide": "left", "color": "3", "label": "JavaScript"}, {"id": "f52c681eca9a6ea1", "fromNode": "4e8ce2b4d9d0ea7f", "fromSide": "right", "toNode": "b68a95402d8073ec", "toSide": "left", "color": "#5a2dd7", "label": "<PERSON>"}, {"id": "667a7998d3dd5832", "fromNode": "b68a95402d8073ec", "fromSide": "right", "toNode": "31017c50ee4c898e", "toSide": "left", "color": "#5a2dd7", "label": "<PERSON>"}, {"id": "9f818350d4addf48", "fromNode": "31017c50ee4c898e", "fromSide": "right", "toNode": "1fc78359e95566d8", "toSide": "left", "color": "#5a2dd7", "label": "<PERSON>"}, {"id": "9a6d2e406a1fc0c6", "fromNode": "1fc78359e95566d8", "fromSide": "right", "toNode": "dfb09c7c972c753a", "toSide": "left", "color": "#5a2dd7"}, {"id": "e8d8cb6a027fc168", "fromNode": "dfb09c7c972c753a", "fromSide": "right", "toNode": "d5356319b6d651fc", "toSide": "left", "color": "#5a2dd7"}, {"id": "4507ab35807e2443", "fromNode": "d5356319b6d651fc", "fromSide": "right", "toNode": "53cc6404880b749c", "toSide": "left", "color": "#5a2dd7"}, {"id": "02ce38ace93fd0ff", "fromNode": "53cc6404880b749c", "fromSide": "right", "toNode": "de15f44d4ed54bd3", "toSide": "left", "color": "#5a2dd7"}, {"id": "93a06ac09e4948b0", "fromNode": "012771e13edd4f14", "fromSide": "right", "toNode": "124dd9373eb9093a", "toSide": "left", "color": "#5a2dd7", "label": "<PERSON>"}, {"id": "ee030c1cbc5daeeb", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "012771e13edd4f14", "toSide": "left", "color": "#5a2dd7", "label": "<PERSON>"}, {"id": "1be4469cbbf64a3a", "fromNode": "243367b7f0f47032", "fromSide": "right", "toNode": "4e8ce2b4d9d0ea7f", "toSide": "left", "color": "#7934b2"}, {"id": "82f0662ad325dfd9", "fromNode": "9a3fb98a45c97e6d", "fromSide": "right", "toNode": "4e8ce2b4d9d0ea7f", "toSide": "left", "color": "#c63a0c"}, {"id": "d414934c11e21f1a", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "4e8ce2b4d9d0ea7f", "toSide": "left", "color": "#17706e"}, {"id": "69d2a69543a5bf0e", "fromNode": "4336571ec0523152", "fromSide": "right", "toNode": "4e8ce2b4d9d0ea7f", "toSide": "left", "color": "5"}, {"id": "4f59810426ada1cc", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "a79cd7d25e98469d", "toSide": "left", "color": "#17706e"}, {"id": "c69180b2c3f8e1c8", "fromNode": "a79cd7d25e98469d", "fromSide": "right", "toNode": "31b99da9d06a216a", "toSide": "left", "color": "1", "label": "AppleScript"}, {"id": "b82ed3c02792fd46", "fromNode": "981a0fe131abc6c3", "fromSide": "right", "toNode": "d0b362ec28377aeb", "toSide": "left", "color": "3"}, {"id": "c7f82913edb1f1c7", "fromNode": "f2e75a4126a8eebc", "fromSide": "right", "toNode": "9b14444d8b1b0a6b", "toSide": "left", "color": "#8f385b"}, {"id": "8af3c4f175464bfe", "fromNode": "067d433ea664c147", "fromSide": "right", "toNode": "9b14444d8b1b0a6b", "toSide": "left", "color": "#ec88d7"}, {"id": "83e883cb6bb46aa9", "fromNode": "9b14444d8b1b0a6b", "fromSide": "right", "toNode": "5168f1c3773b695a", "toSide": "left", "color": "#2a7e7d", "label": "Delphi"}, {"id": "0b558e1c3600870d", "fromNode": "5168f1c3773b695a", "fromSide": "right", "toNode": "efb89277495d3966", "toSide": "left", "color": "#2a7e7d", "label": "Delphi"}, {"id": "964728e8fd286ce1", "fromNode": "efb89277495d3966", "fromSide": "right", "toNode": "4c921eec6e3ded45", "toSide": "left", "color": "#2a7e7d"}, {"id": "085496e69c90cac5", "fromNode": "598170a11c764cc1", "fromSide": "right", "toNode": "624bcb2add4bb843", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "357435942a824998", "fromNode": "621062d25aaa579e", "fromSide": "right", "toNode": "598170a11c764cc1", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "9c57e7437ef14ecc", "fromNode": "54d30afabcf9b14d", "fromSide": "right", "toNode": "621062d25aaa579e", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "b12d590dd849e17b", "fromNode": "624bcb2add4bb843", "fromSide": "right", "toNode": "88539042eb11263e", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "f8fa2bf8a34194a6", "fromNode": "88539042eb11263e", "fromSide": "right", "toNode": "37b864a2f4b80e84", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "6b830bc316b972d9", "fromNode": "37b864a2f4b80e84", "fromSide": "right", "toNode": "dbb708209916a992", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "a6cf82fb7d746380", "fromNode": "dbb708209916a992", "fromSide": "right", "toNode": "1644f236f6d21fe1", "toSide": "left", "color": "#8ecccb", "label": "PHP"}, {"id": "2957462bc4bd88ea", "fromNode": "4336571ec0523152", "fromSide": "right", "toNode": "54d30afabcf9b14d", "toSide": "left", "color": "5"}, {"id": "2d3b1a8d742a7e92", "fromNode": "067d433ea664c147", "fromSide": "right", "toNode": "fe129f12c3dd5ad0", "toSide": "left", "color": "#ec88d7"}, {"id": "423617a970d17796", "fromNode": "fe129f12c3dd5ad0", "fromSide": "right", "toNode": "b7653d457dc0ac31", "toSide": "left", "color": "1", "label": "VBScript"}, {"id": "95e36ab204877f92", "fromNode": "fe129f12c3dd5ad0", "fromSide": "right", "toNode": "53bfc36400609496", "toSide": "left", "color": "1"}, {"id": "5dfdf1e8baf41700", "fromNode": "4da7574c2ad794de", "fromSide": "right", "toNode": "9a3fb98a45c97e6d", "toSide": "left", "color": "#ab2b31"}, {"id": "0e962a61a4848981", "fromNode": "2cf8964ff0fe18ee", "fromSide": "right", "toNode": "53bfc36400609496", "toSide": "left", "color": "#add02f"}, {"id": "68c98141fbdcd35d", "fromNode": "116cbc3e5cee49c1", "fromSide": "right", "toNode": "77c7f786fe2098f9", "toSide": "left", "color": "3", "label": "JScript"}, {"id": "df5e771319319f3e", "fromNode": "53bfc36400609496", "fromSide": "right", "toNode": "116cbc3e5cee49c1", "toSide": "left", "color": "3"}, {"id": "048037ee1add0187", "fromNode": "54795c07ddd90b14", "fromSide": "right", "toNode": "db8b6c5cfbd8f39b", "toSide": "left", "color": "4"}, {"id": "3c65cd2d7a43c3ae", "fromNode": "53bfc36400609496", "fromSide": "right", "toNode": "0d48d40b60fbba43", "toSide": "left", "color": "3"}, {"id": "41538a1950f8d062", "fromNode": "0d48d40b60fbba43", "fromSide": "right", "toNode": "67bc870d88f5ef05", "toSide": "left", "color": "1", "label": "ECMAScript"}, {"id": "6e6c0a43f0a45ce4", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "5bffc1471db5acc5", "toSide": "left", "color": "1"}, {"id": "144d34b5b775cd13", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "77c7f786fe2098f9", "toSide": "left", "color": "1"}, {"id": "c596401d4d7f4dcf", "fromNode": "4d6b94bae22ddd2e", "fromSide": "right", "toNode": "ef33f23ec66a377c", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "370f7bd74a25d5bb", "fromNode": "ef33f23ec66a377c", "fromSide": "right", "toNode": "e6b999b3475a8865", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "88bccd506174cb3a", "fromNode": "e6b999b3475a8865", "fromSide": "right", "toNode": "4425238972f5c331", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "bb76f212074bbce7", "fromNode": "4425238972f5c331", "fromSide": "right", "toNode": "48b4a01ccfd9708f", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "e1b3cf39b78318ec", "fromNode": "48b4a01ccfd9708f", "fromSide": "right", "toNode": "0b32f5cc6d1ab3c1", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "1a9ae7a70bdf645b", "fromNode": "0b32f5cc6d1ab3c1", "fromSide": "right", "toNode": "312f4500eef85efe", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "0fb76f8520987762", "fromNode": "312f4500eef85efe", "fromSide": "right", "toNode": "8be9a1e255964736", "toSide": "left", "color": "#99689c", "label": "C#"}, {"id": "b32db2263d98c2f5", "fromNode": "2cf8964ff0fe18ee", "fromSide": "right", "toNode": "4d6b94bae22ddd2e", "toSide": "left", "color": "#add02f"}, {"id": "f8899e2c9d728613", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "4d6b94bae22ddd2e", "toSide": "left", "color": "2"}, {"id": "a48eef351ae25a6b", "fromNode": "b7e62094b9ac003d", "fromSide": "right", "toNode": "848ea3d3ab1ac8f1", "toSide": "left", "color": "4", "label": "ActionScript"}, {"id": "59eb3f6ef8dcac23", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "b7e62094b9ac003d", "toSide": "left", "color": "1"}, {"id": "0024ef56cec3fbdb", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "b7e62094b9ac003d", "toSide": "left", "color": "2"}, {"id": "584889b12c86b52f", "fromNode": "07ea68796b915cb6", "fromSide": "right", "toNode": "e4be1f6f5d64fc91", "toSide": "left", "color": "2", "label": "<PERSON>"}, {"id": "830c4aadfa6f2188", "fromNode": "e4be1f6f5d64fc91", "fromSide": "right", "toNode": "f2e75a4126a8eebc", "toSide": "left", "color": "2"}, {"id": "f4c098fcaac18419", "fromNode": "1543e3448d3337e8", "fromSide": "right", "toNode": "3fe2b5685b2c9a14", "toSide": "left", "color": "2", "label": "ABAP"}, {"id": "d1b8401ab4ea2a94", "fromNode": "93b280215ca85ceb", "fromSide": "right", "toNode": "1543e3448d3337e8", "toSide": "left", "color": "2", "label": "ABAP"}, {"id": "21b91ea9b7437dc1", "fromNode": "1fd4d16d9083326b", "fromSide": "right", "toNode": "93b280215ca85ceb", "toSide": "left", "color": "2", "label": "ABAP"}, {"id": "79e0739c7d9c973b", "fromNode": "dfc2993b5696074e", "fromSide": "right", "toNode": "1fd4d16d9083326b", "toSide": "left", "color": "1"}, {"id": "5403bfb12c8abcad", "fromNode": "75cbf67be9d19646", "fromSide": "right", "toNode": "1fd4d16d9083326b", "toSide": "left", "color": "#6e2e7f"}, {"id": "3f15a2d626f5c9a3", "fromNode": "75cbf67be9d19646", "fromSide": "right", "toNode": "51970f5cd6ed434a", "toSide": "left", "color": "#6e2e7f", "label": "SQL"}, {"id": "46c9f1533b52c299", "fromNode": "51970f5cd6ed434a", "fromSide": "right", "toNode": "8b40f8b7f52d6086", "toSide": "left", "color": "#6e2e7f", "label": "SQL"}, {"id": "c3fb27a60e8cc302", "fromNode": "8b40f8b7f52d6086", "fromSide": "right", "toNode": "065d3d4032874a83", "toSide": "left", "color": "#6e2e7f", "label": "SQL"}, {"id": "ec668293cba149ed", "fromNode": "065d3d4032874a83", "fromSide": "right", "toNode": "9d90620a710b90b9", "toSide": "left", "color": "#6e2e7f", "label": "SQL"}, {"id": "15cdd0813bce624d", "fromNode": "404c27a6799809f6", "fromSide": "right", "toNode": "977e2ea3d413313b", "toSide": "left", "color": "5", "label": "Clojure"}, {"id": "799acd314db39fda", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "404c27a6799809f6", "toSide": "left", "color": "2"}, {"id": "09fae3c7ec28345b", "fromNode": "94776504e026acbe", "fromSide": "right", "toNode": "404c27a6799809f6", "toSide": "left", "color": "6"}, {"id": "cf0264aa0979c9e7", "fromNode": "e6b999b3475a8865", "fromSide": "right", "toNode": "404c27a6799809f6", "toSide": "left", "color": "#99689c"}, {"id": "e88ccad557d24aaa", "fromNode": "977e2ea3d413313b", "fromSide": "right", "toNode": "aa955875eebdf9e4", "toSide": "left", "color": "5", "label": "Clojure"}, {"id": "e8eb9742bc727261", "fromNode": "211ef1ebaa714e4b", "fromSide": "right", "toNode": "0f8ff1e109fd91ed", "toSide": "left", "color": "5"}, {"id": "19e659f230a7bc39", "fromNode": "0f8ff1e109fd91ed", "fromSide": "right", "toNode": "befbe71a20a81577", "toSide": "left", "color": "5"}, {"id": "c1f0772ed04a67e3", "fromNode": "befbe71a20a81577", "fromSide": "right", "toNode": "d0e12572d9992565", "toSide": "left", "color": "5", "label": "Coffeescript"}, {"id": "f02dd47971bcb6ce", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "404c27a6799809f6", "toSide": "left", "color": "#5a2dd7"}, {"id": "5ed0f1fb8265bc49", "fromNode": "0c09a058cf468575", "fromSide": "right", "toNode": "211ef1ebaa714e4b", "toSide": "left", "color": "#965494"}, {"id": "7a49a76d2e29f06f", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "211ef1ebaa714e4b", "toSide": "left", "color": "#5a2dd7"}, {"id": "b56d9828dc297d8b", "fromNode": "5bffc1471db5acc5", "fromSide": "right", "toNode": "211ef1ebaa714e4b", "toSide": "left", "color": "3"}, {"id": "2636a34df9dcfa0f", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "211ef1ebaa714e4b", "toSide": "left", "color": "#c63a0c"}, {"id": "020790d8e7085cfd", "fromNode": "af20d00003e241d9", "fromSide": "right", "toNode": "a21b9982f5487a1f", "toSide": "left", "color": "3", "label": "Crystal"}, {"id": "0ab3b544627e5740", "fromNode": "a21b9982f5487a1f", "fromSide": "right", "toNode": "3beaeaa8aeed5b4d", "toSide": "left", "color": "3", "label": "Crystal"}, {"id": "00b3ee00bcef359b", "fromNode": "3beaeaa8aeed5b4d", "fromSide": "right", "toNode": "a9221cb0f64c630c", "toSide": "left", "color": "3", "label": "Crystal"}, {"id": "e0a8374ed35f7b48", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "af20d00003e241d9", "toSide": "left", "color": "#5a2dd7"}, {"id": "640de467d5c9d7f7", "fromNode": "9d2ebdca1296a70d", "fromSide": "right", "toNode": "ddfb4253929db68f", "toSide": "left", "color": "4", "label": "Go"}, {"id": "ad536652e0ce9904", "fromNode": "df30a37c6503152f", "fromSide": "right", "toNode": "0881f5616edb92cd", "toSide": "left", "color": "4", "label": "Go"}, {"id": "fec2e4d080c721ff", "fromNode": "ddfb4253929db68f", "fromSide": "right", "toNode": "df30a37c6503152f", "toSide": "left", "color": "4", "label": "Go"}, {"id": "f44f93fbb79ccd23", "fromNode": "75b8fe9096fc6d4f", "fromSide": "right", "toNode": "9d2ebdca1296a70d", "toSide": "left", "color": "#29b525"}, {"id": "085170876bd28a79", "fromNode": "9427e775014f5e0e", "fromSide": "right", "toNode": "da94bfd801327a06", "toSide": "left", "color": "1", "label": "Dart"}, {"id": "d9670a59dc78bb10", "fromNode": "da94bfd801327a06", "fromSide": "right", "toNode": "a5ecc8d29d99b62a", "toSide": "left", "color": "1", "label": "Dart"}, {"id": "f50cf987c1c0b853", "fromNode": "a5ecc8d29d99b62a", "fromSide": "right", "toNode": "5a598d7bf6c42f2c", "toSide": "left", "color": "1", "label": "Dart"}, {"id": "02582da9269ffb3f", "fromNode": "5bffc1471db5acc5", "fromSide": "right", "toNode": "9427e775014f5e0e", "toSide": "left", "color": "3"}, {"id": "b476adad4b844021", "fromNode": "75b8fe9096fc6d4f", "fromSide": "right", "toNode": "9427e775014f5e0e", "toSide": "left", "color": "#29b525"}, {"id": "5eba68067b8aac45", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "9427e775014f5e0e", "toSide": "left", "color": "#17706e"}, {"id": "e7f048672e363016", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "9427e775014f5e0e", "toSide": "left", "color": "#5a2dd7"}, {"id": "17680c5da6841cad", "fromNode": "7a3081e419c922e8", "fromSide": "right", "toNode": "df2054c158055f86", "toSide": "left", "color": "3", "label": "<PERSON><PERSON><PERSON>"}, {"id": "032beb11584bede2", "fromNode": "df2054c158055f86", "fromSide": "right", "toNode": "f4564a09fc419449", "toSide": "left", "color": "3", "label": "<PERSON><PERSON><PERSON>"}, {"id": "574b88b0436fb5db", "fromNode": "977e2ea3d413313b", "fromSide": "right", "toNode": "7a3081e419c922e8", "toSide": "left", "color": "5"}, {"id": "721531d872ca1b7f", "fromNode": "eed02d108e6dad2e", "fromSide": "right", "toNode": "ffae12eaa3e2ccc6", "toSide": "left", "color": "#9d3e0b", "label": "Erl<PERSON>"}, {"id": "4222205324c591d0", "fromNode": "ffae12eaa3e2ccc6", "fromSide": "right", "toNode": "0118135e12da1bf5", "toSide": "left", "color": "#9d3e0b", "label": "Erl<PERSON>"}, {"id": "0f53784076f93e06", "fromNode": "0118135e12da1bf5", "fromSide": "right", "toNode": "781e60da7e8ab2e6", "toSide": "left", "color": "#9d3e0b", "label": "Erl<PERSON>"}, {"id": "84c0f576b6a4d023", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "eed02d108e6dad2e", "toSide": "left", "color": "6"}, {"id": "89e813b204edfd5b", "fromNode": "7d753ded1a1f1d46", "fromSide": "right", "toNode": "eed02d108e6dad2e", "toSide": "left", "color": "#17706e"}, {"id": "5b48f8b0eb814260", "fromNode": "7b053f632b22f37b", "fromSide": "right", "toNode": "eed02d108e6dad2e", "toSide": "left", "color": "#92c256"}, {"id": "1038794e67d4974c", "fromNode": "eed02d108e6dad2e", "fromSide": "right", "toNode": "7a3081e419c922e8", "toSide": "left", "color": "#9d3e0b"}, {"id": "07125e4ed417bedb", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "7a3081e419c922e8", "toSide": "left", "color": "#5a2dd7"}, {"id": "ee8621653ce4f7b4", "fromNode": "bcb40398fe58c522", "fromSide": "right", "toNode": "13dfe0e5d60ff3f9", "toSide": "left", "color": "3", "label": "F#"}, {"id": "4bdc27db8f75a1e5", "fromNode": "13dfe0e5d60ff3f9", "fromSide": "right", "toNode": "9839bec3abf0a4fd", "toSide": "left", "color": "3", "label": "F#"}, {"id": "3eda14379d94e4b1", "fromNode": "9839bec3abf0a4fd", "fromSide": "right", "toNode": "b07b5af5a8c1927f", "toSide": "left", "color": "3", "label": "F#"}, {"id": "f8c4a08db5b33647", "fromNode": "eed02d108e6dad2e", "fromSide": "right", "toNode": "bcb40398fe58c522", "toSide": "left", "color": "#9d3e0b"}, {"id": "12eb6b23f9cc66da", "fromNode": "52f8df88ab92656b", "fromSide": "right", "toNode": "bcb40398fe58c522", "toSide": "left", "color": "#965494"}, {"id": "f1345f78583c2a82", "fromNode": "9a3fb98a45c97e6d", "fromSide": "right", "toNode": "bcb40398fe58c522", "toSide": "left", "color": "#c63a0c"}, {"id": "732ec4997550bb53", "fromNode": "db8b6c5cfbd8f39b", "fromSide": "right", "toNode": "bcb40398fe58c522", "toSide": "left", "color": "2"}, {"id": "e7fa232f69df78d7", "fromNode": "74987209c71e3abb", "fromSide": "right", "toNode": "4a2eb6e04e97a694", "toSide": "left", "color": "5", "label": "Scala"}, {"id": "a73ed5169c6813ae", "fromNode": "4a2eb6e04e97a694", "fromSide": "right", "toNode": "16c0a8142b9f9671", "toSide": "left", "color": "5", "label": "Scala"}, {"id": "f82463f82288da45", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "74987209c71e3abb", "toSide": "left", "color": "2"}, {"id": "769dca72be652088", "fromNode": "0c09a058cf468575", "fromSide": "right", "toNode": "74987209c71e3abb", "toSide": "left", "color": "#965494"}, {"id": "c3c71b646c7a0b39", "fromNode": "94776504e026acbe", "fromSide": "right", "toNode": "74987209c71e3abb", "toSide": "left", "color": "6"}, {"id": "c88ee1841ab26408", "fromNode": "6777c944f3b30628", "fromSide": "right", "toNode": "21fc09aa75de425a", "toSide": "left", "color": "1", "label": "GLSL"}, {"id": "7b98bc75451237de", "fromNode": "21fc09aa75de425a", "fromSide": "right", "toNode": "59a53ebc6331f98f", "toSide": "left", "color": "1", "label": "GLSL"}, {"id": "2b2ec3c045fd3c6a", "fromNode": "94e7ade9b305e1b2", "fromSide": "right", "toNode": "6777c944f3b30628", "toSide": "left", "color": "#29b525"}, {"id": "a24952e3124f59dc", "fromNode": "b0e6c7ae90b2b156", "fromSide": "right", "toNode": "a72d8d9711be19f2", "toSide": "left", "color": "1", "label": "HLSL"}, {"id": "b72edc0b11e21644", "fromNode": "a72d8d9711be19f2", "fromSide": "right", "toNode": "5d002e1cf03785bd", "toSide": "left", "color": "1"}, {"id": "801443cd9ab76050", "fromNode": "5d002e1cf03785bd", "fromSide": "right", "toNode": "d3d7c2ee5c9ae503", "toSide": "left", "color": "1", "label": "HLSL"}, {"id": "ab5ebec50f68ce0d", "fromNode": "94e7ade9b305e1b2", "fromSide": "right", "toNode": "b0e6c7ae90b2b156", "toSide": "left", "color": "#29b525"}, {"id": "0afcba789b5d2bdb", "fromNode": "0cf1cef6c531e05a", "fromSide": "right", "toNode": "223f3e25db2dba97", "toSide": "left", "color": "4", "label": "GraphQL"}, {"id": "cae93d7128ca828b", "fromNode": "223f3e25db2dba97", "fromSide": "right", "toNode": "42c17de94d9e8e09", "toSide": "left", "color": "4", "label": "GraphQL"}, {"id": "de1f82b7ba403939", "fromNode": "42c17de94d9e8e09", "fromSide": "right", "toNode": "ab97f4a7122f26f7", "toSide": "left", "color": "4", "label": "GraphQL"}, {"id": "580557d8479d2634", "fromNode": "75cbf67be9d19646", "fromSide": "right", "toNode": "0cf1cef6c531e05a", "toSide": "left", "color": "#6e2e7f"}, {"id": "94d240083d98ba29", "fromNode": "5bffc1471db5acc5", "fromSide": "right", "toNode": "0cf1cef6c531e05a", "toSide": "left", "color": "3"}, {"id": "1ee7c252bcf45b0e", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "5ea953ff6ecb5f55", "toSide": "left", "color": "1"}, {"id": "6c327c00ab6e11ec", "fromNode": "5ea953ff6ecb5f55", "fromSide": "right", "toNode": "a06e66ff2c8cadbb", "toSide": "left", "color": "5", "label": "JSON"}, {"id": "634de4403ab6bb20", "fromNode": "a06e66ff2c8cadbb", "fromSide": "right", "toNode": "6489e529978b5b94", "toSide": "left", "color": "5", "label": "JSON"}, {"id": "1c49fc052ead354f", "fromNode": "6489e529978b5b94", "fromSide": "right", "toNode": "048da021f06b5e90", "toSide": "left", "color": "5", "label": "JSON"}, {"id": "caa215e1b8486bb8", "fromNode": "a06e66ff2c8cadbb", "fromSide": "right", "toNode": "0cf1cef6c531e05a", "toSide": "left", "color": "5"}, {"id": "488a0e400d165740", "fromNode": "fd926a42e3504bed", "fromSide": "right", "toNode": "5cda45bd40761d9b", "toSide": "left", "color": "4", "label": "Groovy"}, {"id": "3eb82eda2e2b3de2", "fromNode": "5cda45bd40761d9b", "fromSide": "right", "toNode": "7558fbbec59a2eae", "toSide": "left", "color": "4", "label": "Groovy"}, {"id": "0f6e2fba89568230", "fromNode": "7558fbbec59a2eae", "fromSide": "right", "toNode": "904c8dab955666cb", "toSide": "left", "color": "4", "label": "Groovy"}, {"id": "674e1f0a45199611", "fromNode": "904c8dab955666cb", "fromSide": "right", "toNode": "dfe2b9aa04e2b9aa", "toSide": "left", "color": "4", "label": "Groovy"}, {"id": "f43c404d6c3c3930", "fromNode": "dfe2b9aa04e2b9aa", "fromSide": "right", "toNode": "a8797e67856f7a7c", "toSide": "left", "color": "4", "label": "Groovy"}, {"id": "03fc5cf020b90090", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "fd926a42e3504bed", "toSide": "left", "color": "2"}, {"id": "9f10ad5d26ca161a", "fromNode": "dfb09c7c972c753a", "fromSide": "right", "toNode": "fd926a42e3504bed", "toSide": "left", "color": "#5a2dd7"}, {"id": "c53868e38a794b1b", "fromNode": "7d6163007a3c9b5b", "fromSide": "right", "toNode": "df00c23eaf0f9864", "toSide": "left", "color": "4", "label": "HTML"}, {"id": "8a985cf56d966329", "fromNode": "df00c23eaf0f9864", "fromSide": "right", "toNode": "a06eed9b4410f7ce", "toSide": "left", "color": "4", "label": "HTML"}, {"id": "80cd223409b3460c", "fromNode": "b3d658daaec94691", "fromSide": "right", "toNode": "061f56f8d5b215ae", "toSide": "left", "color": "4", "label": "HTML"}, {"id": "d2909ef98e7a3d84", "fromNode": "33a316e45de25c27", "fromSide": "right", "toNode": "7d6163007a3c9b5b", "toSide": "left", "color": "#2f7f24"}, {"id": "b96c0f48d34c4548", "fromNode": "a858f3d3cf18bd30", "fromSide": "right", "toNode": "33a316e45de25c27", "toSide": "left", "color": "#125d97"}, {"id": "b12479564d4e41a1", "fromNode": "01904ed5d4245b88", "fromSide": "right", "toNode": "ca01d4a6afaea3c2", "toSide": "left", "color": "3", "label": "XML"}, {"id": "828205cf76e2bc80", "fromNode": "ca01d4a6afaea3c2", "fromSide": "right", "toNode": "ad882e76279e4f27", "toSide": "left", "color": "3", "label": "XML"}, {"id": "0dad2f53347bfdf4", "fromNode": "ad882e76279e4f27", "fromSide": "right", "toNode": "4f54ccf8f003e4e2", "toSide": "left", "color": "3", "label": "XML"}, {"id": "9957f23b02983922", "fromNode": "33a316e45de25c27", "fromSide": "right", "toNode": "01904ed5d4245b88", "toSide": "left", "color": "#2f7f24"}, {"id": "9307354976d84c53", "fromNode": "3a8d2c71a1bc3039", "fromSide": "right", "toNode": "35501f0be5c6700e", "toSide": "left", "color": "6", "label": "<PERSON>"}, {"id": "9c5c78f22211de4a", "fromNode": "35501f0be5c6700e", "fromSide": "right", "toNode": "bf2099d3d9ab0d0e", "toSide": "left", "color": "6", "label": "<PERSON>"}, {"id": "dbddd92ad9838e81", "fromNode": "19a4291aefd0323b", "fromSide": "right", "toNode": "3a8d2c71a1bc3039", "toSide": "left", "color": "6"}, {"id": "685c126b11bf9a5b", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "3a8d2c71a1bc3039", "toSide": "left", "color": "6"}, {"id": "2e209ea50f399983", "fromNode": "6f595d87160f13b0", "fromSide": "right", "toNode": "4d9d431946c00799", "toSide": "left", "color": "6"}, {"id": "773eab6ec0dc3c15", "fromNode": "34d8fd541ccb3255", "fromSide": "right", "toNode": "4d9d431946c00799", "toSide": "left", "color": "3"}, {"id": "ccc0ccfeb4d8a50d", "fromNode": "4d9d431946c00799", "fromSide": "right", "toNode": "300c92e7d2a8008d", "toSide": "left", "color": "4", "label": "Mathematica"}, {"id": "3e1f462f317b2c11", "fromNode": "5b8121e38c8bc7c8", "fromSide": "right", "toNode": "071e22882fe6f63d", "toSide": "left", "color": "4", "label": "MATLAB"}, {"id": "bd3ac494b1f2a733", "fromNode": "3c914e72ce82d1d0", "fromSide": "right", "toNode": "5b8121e38c8bc7c8", "toSide": "left", "color": "2"}, {"id": "841e6bbc282b3515", "fromNode": "34d8fd541ccb3255", "fromSide": "right", "toNode": "5b8121e38c8bc7c8", "toSide": "left", "color": "3"}, {"id": "2c5671a307768035", "fromNode": "c9b597feebc31bab", "fromSide": "right", "toNode": "ca5c149918a677c7", "toSide": "left", "color": "1", "label": "S"}, {"id": "c4557e491f5028fb", "fromNode": "24c6b4c401556241", "fromSide": "right", "toNode": "da56b891283079dd", "toSide": "left", "color": "2", "label": "R"}, {"id": "7890eca13d9c1fa5", "fromNode": "ca5c149918a677c7", "fromSide": "right", "toNode": "24c6b4c401556241", "toSide": "left", "color": "1"}, {"id": "f766f1725ff1deea", "fromNode": "24c6b4c401556241", "fromSide": "right", "toNode": "3a8d2c71a1bc3039", "toSide": "left", "color": "2"}, {"id": "125c28b83a94457a", "fromNode": "5b8121e38c8bc7c8", "fromSide": "right", "toNode": "3a8d2c71a1bc3039", "toSide": "left"}, {"id": "a7daddd035d05fda", "fromNode": "4d9d431946c00799", "fromSide": "right", "toNode": "3a8d2c71a1bc3039", "toSide": "left", "color": "4"}, {"id": "25f1435956f678a7", "fromNode": "9852d7c16dee2b5a", "fromSide": "right", "toNode": "ed29a9785d664efa", "toSide": "left", "color": "2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "b717e735dbad6f3c", "fromNode": "ed29a9785d664efa", "fromSide": "right", "toNode": "48f645ba98797e7a", "toSide": "left", "color": "2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "83020eef180dcc44", "fromNode": "243367b7f0f47032", "fromSide": "right", "toNode": "48f645ba98797e7a", "toSide": "left", "color": "#7934b2"}, {"id": "dc55f3f9b7b0b67c", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "9852d7c16dee2b5a", "toSide": "left", "color": "2"}, {"id": "0caeba22487994de", "fromNode": "74987209c71e3abb", "fromSide": "right", "toNode": "9852d7c16dee2b5a", "toSide": "left", "color": "#54dfdd"}, {"id": "937a961f9a2eac54", "fromNode": "de1b0df301cc15f8", "fromSide": "right", "toNode": "9852d7c16dee2b5a", "toSide": "left", "color": "#ec88d7"}, {"id": "b77a4f4b937c8a32", "fromNode": "e682cf7c8422a754", "fromSide": "right", "toNode": "296e8f5e4e2fdb6a", "toSide": "left", "color": "5", "label": "<PERSON><PERSON>"}, {"id": "11c1a912a2679932", "fromNode": "296e8f5e4e2fdb6a", "fromSide": "right", "toNode": "08bae566db2b440a", "toSide": "left", "color": "5", "label": "<PERSON><PERSON>"}, {"id": "d83b2d0f4616859d", "fromNode": "08bae566db2b440a", "fromSide": "right", "toNode": "1e879ca6dd1aefd5", "toSide": "left", "color": "5", "label": "<PERSON><PERSON>"}, {"id": "9fa2cd957807d97b", "fromNode": "1e879ca6dd1aefd5", "fromSide": "right", "toNode": "908091c0f2215d44", "toSide": "left", "color": "5", "label": "<PERSON><PERSON>"}, {"id": "ee814d659b235fa4", "fromNode": "908091c0f2215d44", "fromSide": "right", "toNode": "0c9ffb04865c2238", "toSide": "left", "color": "5", "label": "<PERSON><PERSON>"}, {"id": "c204fd68cf81c96b", "fromNode": "c2135b11ad0c621a", "fromSide": "right", "toNode": "e682cf7c8422a754", "toSide": "left", "color": "4"}, {"id": "ace643bb40eef850", "fromNode": "a4941f76b6a4712b", "fromSide": "right", "toNode": "e682cf7c8422a754", "toSide": "left", "color": "#1d6d35"}, {"id": "85a79957de579c82", "fromNode": "88b9dcaffabe4cad", "fromSide": "right", "toNode": "e682cf7c8422a754", "toSide": "left", "color": "#f04014"}, {"id": "b556e3bd3977593d", "fromNode": "2cf8964ff0fe18ee", "fromSide": "right", "toNode": "e682cf7c8422a754", "toSide": "left"}, {"id": "7b8f524dbe662a76", "fromNode": "5635c7fc9f4e5b10", "fromSide": "right", "toNode": "e682cf7c8422a754", "toSide": "left", "color": "#c26f0f"}, {"id": "6d7b453ef47dd754", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "ffd33d64cfb9372b", "toSide": "left", "color": "#c63a0c"}, {"id": "25254a6d28268ab4", "fromNode": "1480d0eeeda1a7ec", "fromSide": "right", "toNode": "ffd33d64cfb9372b", "toSide": "left", "color": "#add02f"}, {"id": "2860ca60e53dc6e1", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "13c48ce0ccc4e4eb", "toSide": "left", "color": "#c63a0c"}, {"id": "f182e735f5ba7ea7", "fromNode": "419c93edd0eb2437", "fromSide": "right", "toNode": "13c48ce0ccc4e4eb", "toSide": "left", "color": "#dd3c8a"}, {"id": "c1539d1eeda2e567", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "13c48ce0ccc4e4eb", "toSide": "left", "color": "6"}, {"id": "e1ec8f41c617b927", "fromNode": "5978ad6799320393", "fromSide": "right", "toNode": "7943403d25c7f545", "toSide": "left", "color": "1", "label": "<PERSON><PERSON>"}, {"id": "42aa819b7fa313ee", "fromNode": "13c48ce0ccc4e4eb", "fromSide": "right", "toNode": "5978ad6799320393", "toSide": "left", "color": "1", "label": "<PERSON><PERSON>"}, {"id": "a41797cf76662edc", "fromNode": "7943403d25c7f545", "fromSide": "right", "toNode": "7fa16c3efd7a8ec8", "toSide": "left", "color": "1", "label": "<PERSON><PERSON>"}, {"id": "1e42f72814df225d", "fromNode": "e92e2fcb3c57af45", "fromSide": "right", "toNode": "0e0dd418219ccfda", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "924fc5569a22ee9c", "fromNode": "0e0dd418219ccfda", "fromSide": "right", "toNode": "9a0b82dc07236b44", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "5a91a70c1946082f", "fromNode": "9a0b82dc07236b44", "fromSide": "right", "toNode": "5f19433353b5f355", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "5754ca9c8e3459f0", "fromNode": "5f19433353b5f355", "fromSide": "right", "toNode": "407490ec330c92a6", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "f1d9d1f66a594a27", "fromNode": "104fe1eb8bbdd840", "fromSide": "right", "toNode": "7aa8a2dbe0c1acdf", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "d497f66146632818", "fromNode": "c5908cfb4a38dc04", "fromSide": "right", "toNode": "104fe1eb8bbdd840", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "078b73102c88a7a3", "fromNode": "407490ec330c92a6", "fromSide": "right", "toNode": "c5908cfb4a38dc04", "toSide": "left", "color": "1", "label": "Scheme"}, {"id": "ff7916b9d85ee547", "fromNode": "243367b7f0f47032", "fromSide": "right", "toNode": "e92e2fcb3c57af45", "toSide": "left", "color": "#7934b2"}, {"id": "c111b79b221ae3fb", "fromNode": "5635c7fc9f4e5b10", "fromSide": "right", "toNode": "e92e2fcb3c57af45", "toSide": "left", "color": "#c26f0f"}, {"id": "d0d99796b1e9ea3e", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "68d45a91eaf2c566", "toSide": "left", "color": "#f9464c"}, {"id": "c42b0c73a00f2b5d", "fromNode": "db8b6c5cfbd8f39b", "fromSide": "right", "toNode": "68d45a91eaf2c566", "toSide": "left", "color": "2"}, {"id": "031b18d6ac6bc434", "fromNode": "db8b6c5cfbd8f39b", "fromSide": "right", "toNode": "a8fa0fba8b69ab60", "toSide": "left", "color": "2", "label": "OCaml"}, {"id": "25cdecfff071f4c4", "fromNode": "a8fa0fba8b69ab60", "fromSide": "right", "toNode": "8afa1ecd55db0c5b", "toSide": "left", "color": "2", "label": "OCaml"}, {"id": "2260f84e61f3b13a", "fromNode": "8afa1ecd55db0c5b", "fromSide": "right", "toNode": "093618ebf485a513", "toSide": "left", "color": "2", "label": "OCaml"}, {"id": "22d88ec3dec7e540", "fromNode": "093618ebf485a513", "fromSide": "right", "toNode": "09b3854769bc18c0", "toSide": "left", "color": "2", "label": "OCaml"}, {"id": "977e9b107ec10381", "fromNode": "f1b64a55016f7858", "fromSide": "right", "toNode": "56d0a32c54410949", "toSide": "left", "color": "2", "label": "Solidity"}, {"id": "1ebb684d0b48bd39", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "f1b64a55016f7858", "toSide": "left"}, {"id": "e4bbfa344d399840", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "f1b64a55016f7858", "toSide": "left", "color": "#c63a0c"}, {"id": "8ff319ff3be7a2ca", "fromNode": "1480d0eeeda1a7ec", "fromSide": "right", "toNode": "f1b64a55016f7858", "toSide": "left", "color": "#add02f"}, {"id": "87dfafca6069ebf0", "fromNode": "fc3ea3a88eaaed35", "fromSide": "right", "toNode": "112518d84753389f", "toSide": "left", "color": "6", "label": "Swift"}, {"id": "ba8b837c9a31d4e3", "fromNode": "112518d84753389f", "fromSide": "right", "toNode": "17b9697bcd50fdfa", "toSide": "left", "color": "6", "label": "Swift"}, {"id": "4b784289c7e58b0f", "fromNode": "17b9697bcd50fdfa", "fromSide": "right", "toNode": "192aa320f03d5a26", "toSide": "left", "color": "6", "label": "Swift"}, {"id": "90a0ed8836e9c38c", "fromNode": "192aa320f03d5a26", "fromSide": "right", "toNode": "993ddc59d9338067", "toSide": "left", "color": "6", "label": "Swift"}, {"id": "e9bd5a2da5fe98db", "fromNode": "0db6648d3f3bd36a", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left", "color": "#ea6e39"}, {"id": "a921d94563fe5b1d", "fromNode": "0bd4da4f6166ea4b", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left"}, {"id": "42684178e641aebb", "fromNode": "52f8df88ab92656b", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left"}, {"id": "4bc5397c2822f8b5", "fromNode": "de15f44d4ed54bd3", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left", "color": "#5a2dd7"}, {"id": "b10b823de4da57e2", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left", "color": "#c63a0c"}, {"id": "d6ccaa6fddcc0b83", "fromNode": "e6b999b3475a8865", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left", "color": "#99689c"}, {"id": "b1868d9a22643bf4", "fromNode": "88b9dcaffabe4cad", "fromSide": "right", "toNode": "fc3ea3a88eaaed35", "toSide": "left", "color": "#f04015"}, {"id": "918d301921e40b1b", "fromNode": "eed02d108e6dad2e", "fromSide": "right", "toNode": "0bd4da4f6166ea4b", "toSide": "left", "color": "#9d3e0b"}, {"id": "426725c702b80598", "fromNode": "2af38e931b72706b", "fromSide": "right", "toNode": "0bd4da4f6166ea4b", "toSide": "left", "color": "#555b06"}, {"id": "7897021157eb9531", "fromNode": "88b9dcaffabe4cad", "fromSide": "right", "toNode": "0bd4da4f6166ea4b", "toSide": "left", "color": "#f04014"}, {"id": "d01dd5cc9a109f39", "fromNode": "0bd4da4f6166ea4b", "fromSide": "right", "toNode": "93176596c697867b", "toSide": "left", "color": "1", "label": "Rust"}, {"id": "6246e60e81dc5a8f", "fromNode": "93176596c697867b", "fromSide": "right", "toNode": "7fed863ddcabbad4", "toSide": "left", "color": "1", "label": "Rust"}, {"id": "bdf2f2b3e8a64e99", "fromNode": "1bfe608a58a7dd8b", "fromSide": "right", "toNode": "054f555173cbbd93", "toSide": "left", "color": "2", "label": "Sather"}, {"id": "f72de1d83ec8a4d6", "fromNode": "054f555173cbbd93", "fromSide": "right", "toNode": "05b1bfdc73169483", "toSide": "left", "color": "2", "label": "Sather\n"}, {"id": "7188ca080432b1d7", "fromNode": "1bfe608a58a7dd8b", "fromSide": "right", "toNode": "0bd4da4f6166ea4b", "toSide": "left", "color": "2"}, {"id": "932ec260f2a651f6", "fromNode": "243367b7f0f47032", "fromSide": "right", "toNode": "1bfe608a58a7dd8b", "toSide": "left", "color": "#7934b2"}, {"id": "7c4a5005a75b529a", "fromNode": "88b9dcaffabe4cad", "fromSide": "right", "toNode": "1bfe608a58a7dd8b", "toSide": "left", "color": "#f04014"}, {"id": "a9bebf55dcba1ca0", "fromNode": "67bc870d88f5ef05", "fromSide": "right", "toNode": "baf656c6fe8b3f8d", "toSide": "left", "color": "1"}, {"id": "96eb1c4d00d8a6a0", "fromNode": "e6b999b3475a8865", "fromSide": "right", "toNode": "baf656c6fe8b3f8d", "toSide": "left", "color": "#99689c"}, {"id": "47cef3475b2b64ac", "fromNode": "3896d32d8f25e590", "fromSide": "right", "toNode": "baf656c6fe8b3f8d", "toSide": "left", "color": "2"}, {"id": "5744a7318f81a191", "fromNode": "0b238612fb0717b4", "fromSide": "right", "toNode": "33a452c2fa1d1983", "toSide": "left", "color": "5", "label": "TypeScript"}, {"id": "b0382588c94c196e", "fromNode": "33a452c2fa1d1983", "fromSide": "right", "toNode": "6e441c0b484e3e84", "toSide": "left", "color": "5", "label": "TypeScript"}, {"id": "e1d71df534622206", "fromNode": "6e441c0b484e3e84", "fromSide": "right", "toNode": "a9edff0b5ce315e8", "toSide": "left", "color": "5", "label": "TypeScript"}, {"id": "fdcb8102baf8c892", "fromNode": "a9edff0b5ce315e8", "fromSide": "right", "toNode": "5cb574c05267f255", "toSide": "left", "color": "5", "label": "TypeScript"}, {"id": "e52c237e214b2999", "fromNode": "bcb40398fe58c522", "fromSide": "right", "toNode": "baf656c6fe8b3f8d", "toSide": "left"}, {"id": "f033cde1ae04df04", "fromNode": "baf656c6fe8b3f8d", "fromSide": "right", "toNode": "0b238612fb0717b4", "toSide": "left", "color": "5", "label": "TypeScript"}, {"id": "99c9e2f59a073bc6", "fromNode": "6795b336f97e14f8", "fromSide": "right", "toNode": "6876efd642d62a97", "toSide": "left", "color": "4", "label": "WebAssembly"}, {"id": "c8e276c6e19fb0d5", "fromNode": "44360019663262fa", "fromSide": "right", "toNode": "6795b336f97e14f8", "toSide": "left", "color": "6"}, {"id": "919a3e00d706d30e", "fromNode": "8d9c29b6030ce739", "fromSide": "right", "toNode": "e1adb42eecb8cf6f", "toSide": "left", "color": "3", "label": "YAML"}, {"id": "f5f016179373ce38", "fromNode": "e1adb42eecb8cf6f", "fromSide": "right", "toNode": "d680d1fb55233ab9", "toSide": "left", "color": "3"}, {"id": "325965553f12ef86", "fromNode": "d680d1fb55233ab9", "fromSide": "right", "toNode": "eae3cda390066fd1", "toSide": "left", "color": "3", "label": "YAML"}, {"id": "f7e3ab348326b774", "fromNode": "28f9764c0f2ac08e", "fromSide": "right", "toNode": "2bde5c7a605fa79b", "toSide": "left", "color": "3", "label": "YAML"}, {"id": "8ca7d7323e42264d", "fromNode": "56e189cc1529d962", "fromSide": "right", "toNode": "8d9c29b6030ce739", "toSide": "left", "color": "#c63a0c"}, {"id": "1660f0f2ab18a6e3", "fromNode": "ce4ad1b916634c2b", "fromSide": "right", "toNode": "8efe7c783a6c9829", "toSide": "left", "color": "1", "label": "Zig"}, {"id": "94da4084db4cbfaa", "fromNode": "8efe7c783a6c9829", "fromSide": "right", "toNode": "71b7a5d0963e8fb9", "toSide": "left", "color": "1", "label": "Zig"}, {"id": "3ec303b8f25659a1", "fromNode": "0bd4da4f6166ea4b", "fromSide": "right", "toNode": "ce4ad1b916634c2b", "toSide": "left"}, {"id": "e97ff9c3584b059d", "fromNode": "1480d0eeeda1a7ec", "fromSide": "right", "toNode": "ce4ad1b916634c2b", "toSide": "left", "color": "#add02f"}, {"id": "bff3d58f695b8aa5", "fromNode": "94e7ade9b305e1b2", "fromSide": "right", "toNode": "ce4ad1b916634c2b", "toSide": "left", "color": "#29b525"}, {"id": "f09c97850891e71f", "fromNode": "9d2ebdca1296a70d", "fromSide": "right", "toNode": "ce4ad1b916634c2b", "toSide": "left", "color": "4"}, {"id": "cf8f4836e0100af1", "fromNode": "a06eed9b4410f7ce", "fromSide": "right", "toNode": "b3d658daaec94691", "toSide": "left"}, {"id": "855b0438197542ce", "fromNode": "0bfba93dbd23eda1", "fromSide": "right", "toNode": "94061c92ba1e27ee", "toSide": "left"}, {"id": "6d18c60e80ab7fe1", "fromNode": "e7de6ea303fe22a9", "fromSide": "right", "toNode": "31e228ae0869ab23", "toSide": "left"}, {"id": "81b6016d50c0162a", "fromNode": "eae3cda390066fd1", "fromSide": "right", "toNode": "28f9764c0f2ac08e", "toSide": "left"}, {"id": "3d6cd9a7f054989c", "fromNode": "02638b7db2030656", "fromSide": "right", "toNode": "45df154795344164", "toSide": "left"}, {"id": "bd8f36c257c10b8a", "fromNode": "d120440c3d5019c9", "fromSide": "right", "toNode": "53bfc36400609496", "toSide": "left", "color": "3"}, {"id": "57c736900b822b8e", "fromNode": "2969730f9e6c65c9", "fromSide": "right", "toNode": "9d2ebdca1296a70d", "toSide": "left", "color": "4"}, {"id": "1643d68fc183e9ad", "fromNode": "2969730f9e6c65c9", "fromSide": "right", "toNode": "13c48ce0ccc4e4eb", "toSide": "left", "color": "4"}, {"id": "4f5b1aefa2a76717", "fromNode": "31e228ae0869ab23", "fromSide": "right", "toNode": "56e189cc1529d962", "toSide": "left", "color": "#452ec2"}, {"id": "b933ae7063bb48f7", "fromNode": "48f645ba98797e7a", "fromSide": "right", "toNode": "b0ebb850902e5a4b", "toSide": "left", "color": "2", "label": "<PERSON><PERSON><PERSON>"}, {"id": "6cde5d73e80a590f", "fromNode": "f4c56070538c7cbe", "fromSide": "right", "toNode": "f04c2a8366585388", "toSide": "left", "color": "#0c5eac", "label": "occam"}, {"id": "13e1b83d4b162999", "fromNode": "f04c2a8366585388", "fromSide": "right", "toNode": "48eb9d0238c2b2c9", "toSide": "left", "color": "#0c5eac", "label": "occam"}, {"id": "e89bd38914a0b06a", "fromNode": "f4c56070538c7cbe", "fromSide": "right", "toNode": "9d2ebdca1296a70d", "toSide": "left", "color": "#0c5eac"}, {"id": "75bc31c6d6fabdb4", "fromNode": "d74a046ba5206b95", "fromSide": "right", "toNode": "fd4ac995fcf25a84", "toSide": "left", "color": "#9536a3"}, {"id": "4ecfd1393355b1a7", "fromNode": "9adbe19d9f4a40e1", "fromSide": "right", "toNode": "fd4ac995fcf25a84", "toSide": "left", "color": "#a8d629"}, {"id": "1e9c39810bc3a854", "fromNode": "fd4ac995fcf25a84", "fromSide": "right", "toNode": "ffc27b4e528c5146", "toSide": "left", "color": "#9536a3"}, {"id": "4b00f86303b9f4ec", "fromNode": "ffc27b4e528c5146", "fromSide": "right", "toNode": "2b85a5912c6b5890", "toSide": "left", "color": "#3c9955"}, {"id": "9f22b4a2bb5f9779", "fromNode": "7518674cd0becd37", "fromSide": "right", "toNode": "753762f9de2b747c", "toSide": "left", "color": "1"}, {"id": "ba20012e772ded46", "fromNode": "7f2d3d7427b5e540", "fromSide": "right", "toNode": "7518674cd0becd37", "toSide": "left", "color": "5"}, {"id": "3d30f3e6b5e971f4", "fromNode": "1b5a3cbe9cb737f7", "fromSide": "right", "toNode": "09ce9b41e225f417", "toSide": "left", "color": "#92389e", "label": "A-0"}, {"id": "7d6fc864c842454b", "fromNode": "af13f34b6f4b1008", "fromSide": "right", "toNode": "7518674cd0becd37", "toSide": "left", "color": "2"}, {"id": "f1d3e8550c06f72c", "fromNode": "09ce9b41e225f417", "fromSide": "right", "toNode": "d74a046ba5206b95", "toSide": "left", "color": "#9536a3", "label": "A-0"}, {"id": "f56e8f006d443f0e", "fromNode": "fd4ac995fcf25a84", "fromSide": "right", "toNode": "59bc0d19da6f728a", "toSide": "left", "color": "#9536a3", "label": "A-0"}, {"id": "c7db28ec1ed38a94", "fromNode": "7f2d3d7427b5e540", "fromSide": "right", "toNode": "af13f34b6f4b1008", "toSide": "left", "color": "5"}, {"id": "90431d23b51abd5e", "fromNode": "b255392eaa337186", "fromSide": "right", "toNode": "7497014be02740b4", "toSide": "left", "color": "3"}, {"id": "1b2b599965e581c0", "fromNode": "7497014be02740b4", "fromSide": "right", "toNode": "08cc8dff9a9718a7", "toSide": "left", "color": "3", "label": "IPL"}, {"id": "c4be528d86cfefa4", "fromNode": "b255392eaa337186", "fromSide": "right", "toNode": "a9dbba8479ad4b1c", "toSide": "left", "color": "3"}, {"id": "85f8858a39c7e4e0", "fromNode": "c2135b11ad0c621a", "fromSide": "right", "toNode": "666130658c516db9", "toSide": "left", "color": "#45cf6e"}, {"id": "d26b6fc630f93642", "fromNode": "d6d099c6cf9074a9", "fromSide": "right", "toNode": "d70d8e78ceb3e694", "toSide": "left", "color": "5", "label": "ALGOL"}, {"id": "6583b0ad0ccde485", "fromNode": "1938ac6a7704881c", "fromSide": "right", "toNode": "d6d099c6cf9074a9", "toSide": "left", "color": "2"}]}