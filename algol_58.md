Link: [ALGOL 58](file:///Users/<USER>/Literature/CS/Languages/algol58_1958.pdf)
Year: 1958

## Reference Language
### Basic symbols
(p. 11)
- *Letters* $\lambda$
- *Figures* $\zeta$ (0,...,9)
- *Delimiters* $\delta$
	- operators $\omega$:
		- arithmetic: $+$ $-$ $\times$ $/$
		- relational: $<$ $\leq$ $=$ $\geq$ $>$ $\neq$
		- logical: $\neg$ $\lor$ $\land$ $\equiv$
		- sequential: `go to` `do` `return` `stop` `for` `if` `or` `if either` `or if`
	- separators $\sigma$: `.` `,` `:` `;` `:=` `=:` $\rightarrow$ $_{10}$ `begin` `end`
	- brackets $\beta$: `( )` `[ ]` $\uparrow$ $\downarrow$
	- declarators $\phi$: `procedure` `array` `switch` `type` `comment`
Strings of $\zeta$ are called (*positive*) *integers* G.
Strings of $\zeta$ and $\lambda$ started from $\lambda$ are called *identifiers* I.
### Expressions
(pp. 11-13)
- (*positive*) *Numbers*: $N \sim G.G_{10} \pm G$
- *Simple Variables*: $V \sim I$
- *Subscripted Variables*: $V \sim I[C]$, where $C \sim E, E, ..., E$ is a *list* of arithmetic expressions E, which occupy one subscript position, called *subscript*
- *Functions*: $F \sim I(P,P,...P)$, where $P,P,...P$ is the ordered list of parameters
- *Arithmetic expressions* E
	- Number, variable or function
		- $E \sim N$
		- $E \sim V$
		- $E \sim F$
	- If $E_1$ and $E_2$ are expressions, the first symbol of which is not + or -
		- $E \sim +E_1$
		- $E \sim -E_2$
		- $E \sim E_1+E_2$
		- $E \sim E_1-E_2$
		- $E \sim E_1\times E_2$
		- $E \sim E_1/E_2$
		- $E \sim E_1\uparrow E_2\downarrow$
		- $E \sim (E_1)$
- *Boolean expressions* B
	- Truth value, variable or function
		- $B \sim 0$ (*false*)
		- $B \sim 1$ (*true*)
		- $B \sim V$
		- $B \sim F$
	- If $E_1$ and $E_2$ are arithmetic expressions
		- $B \sim (E_1 < E_2)$
		- $B \sim (E_1 \leq E_2)$
		- $B \sim (E_1 \neq E_2)$
		- $B \sim (E_1 \geq E_2)$
		- $B \sim (E_1 > E_2)$
		- $B \sim (E_1 = E_2)$
	- If $B_1$ and $B_2$ are expressions
		- $B \sim \neg B_1$
		- $B \sim B_1 \lor B_2$
		- $B \sim B_1 \land B_2$
		- $B \sim B_1 \equiv B_2$
		- $B \sim (B_1)$
### Statements $\Sigma$
(pp. 13-17)
$\Sigma \sim begin\space\Sigma;\Sigma;...;\Sigma\space end$ (begin and end is "statement parentheses")
$\Sigma \sim L:\Sigma$, where L is a *label*, $L\sim I$ or $L \sim G$
$\Sigma \sim L:begin\space\Sigma;\Sigma;...;\Sigma\space end\space L$ (labeled compound statement)
- *Assignment statements*
	- $\Sigma \sim V:=E$ (V should be arithmetic)
	- $\Sigma \sim V:=B$ (V is any)
- *Go to statements*: $\Sigma \sim go\space to\space D$, where D is a *designational* expression, $D \sim L$ or $D \sim I[E]$
- *If statements*: $\Sigma \sim if\space B$
- *For statements*
	- $for\space V:=C$
	- $for\space V:=E_{i_1} (E_{s_1}) E_{e_1}, ..., E_{i_k} (E_{s_k}) E_{e_k}$
- *Alternative statements*
	- $if\space either\space B_1; \Sigma_1; or\space if \space B_2;\Sigma_2; ...; or\space if\space B_k; \Sigma_k\space end$
- *Do statements*: $\Sigma \sim do\space L_1\space L_2\space (S_{\rightarrow}\rightarrow I, ..., S_{\rightarrow}\rightarrow I)$, $S_{\rightarrow}$ are strings w/o ($\rightarrow$) which replace identifiers I in the string of statements from $L_1$ to $L_2$ included
- *Stop statements*:  $\Sigma \sim stop$
- *Return statements*: $\Sigma \sim return$
- *Procedure statements*: $\Sigma \sim I(P_i, P_i, ..., P_i) =: (P_o, P_o, ..., P_o)$, where $P_i$ is an input variable and $P_o$ is an output variable (output value)
### Declarations $\Delta$
(pp. 17-20)
- *Type declarations*: $\Delta \sim type(I,I,...,I,I[],...,I[,],...,I[,,],...)$
- *Array declarations*: $\Delta \sim array(I,I,...,I[C:C\prime],I,I,...,I[C:C\prime],..)$, where I is a name of array and C/C' is a list of lower/upper bounds
- *Switch declarations*: $\Delta \sim switch\space I:= (D_1, D_2, ..., D_n)$
- *Function declarations*: $\Delta \sim I_n(I,I,...,I) := E$, where $I_n$ is a function name
- *Comment declarations*: $\Delta \sim$ `comment` $S_;$, where $S_;$ is any string of symbols not containing the symbol `;`
- *Procedure declarations*: $\Delta \sim procedure\space I(P_i) =: (P_o), I(P_i) =: (P_o), ..., I(P_i) =: (P_o)$
$\Delta;\Delta;...;\Delta\space begin\space\Sigma;\Sigma;...;\Delta;\Delta;...;\Sigma;\Sigma\space end$

## Syntax
(pp. 20-21)
### Basic symbols ($\alpha$)
(pp. 20)
- Delimiters $\delta$
	- Operators $\omega$
		- $\sim$ $+$ $-$ $\times$ $/$ $<$ $\leq$ $=$ $\geq$ $>$ $\neq$ $\neg$ $\lor$ $\land$ $\equiv$
		- $\sim$ `go to` `do` `return` `stop` `for` `if` `or` `if either` `or if`
	- Separators $\sigma \sim .\space,\space:\space;\space:=\space=:\space\rightarrow\space_{10}$ `begin` `end`
	- Brackets $\beta \sim (\space)\space[\space]\space\uparrow\space\downarrow$
	- Declarators $\phi \sim$ `procedure` `array` `switch` `type` `comment`
- Non-delimiters $\mu$
	- Letters $\lambda \sim$ A ... Z, a ... z
	- Digits $\zeta \sim$ 0 ... 9
### Syntactic skeleton
(pp. 20-21)
- Syllables
	- List $C \sim E,E,...E$
	- Simple variable $V \sim I$
	- Subscripted variable $V \sim I[E,E,...E]$
	- $R \sim P,P,P,...,P,P$
	- Function $F \sim I(R)$
	- Statement label $L$
		- $\sim I$
		- $\sim G$
	- Expression $E$
	- Boolean expression $B$
	- Designational expression $D$
		- $\sim L$
		- $\sim I[E]$
	- Parameters $P$
	- Identifier $I \sim \lambda \mu \mu \mu ... \mu \mu$
	- Integer $G \sim \zeta \zeta \zeta ... \zeta$
	- Number $N \sim \zeta \zeta \zeta ... \zeta . \zeta \zeta \zeta ... \zeta _{10} \pm G$
	- Symbol string $S_{\delta} \sim \alpha \alpha \alpha \alpha ... \alpha \alpha$, where $\alpha \neq \delta$
- Statements $\Sigma$
	- Assignment statement
		- $\sim V := E$
		- $\sim V := B$
	- Compound statement $\sim begin\space\Sigma; \Sigma; ...; \Sigma\space end$, where $\Sigma$ is unlabeled
	- Labelled statement $\sim L: \Sigma$, where $\Sigma$ is unlabeled
	- Go to statement $\sim go\space to\space D$
	- Do statement $\sim do\space L_1,L_2 (S_\rightarrow \rightarrow I, S_\rightarrow \rightarrow I, ..., S_\rightarrow \rightarrow I)$
	- Quantifier statement
		- $\sim if\space B$
		- $\sim for\space V:= C$
		- $\sim for\space V:= E(E)E,E(E)E,...,E(E)E$
	- Alternative $\sim if\space either\space B_1;\Sigma_1;or\space if\space B_2;\Sigma_2;or\space if\space B_3;\Sigma_3;...;or\space if\space B_k;\Sigma_k\space end$
	- Stop and return statements
		- $\sim stop$
		- $\sim return$
	- Procedure call statement $\sim I(R) =: (R)$
- Declarations $\Delta$
	- Function declaration $\sim I(R) := E$
	- Switch declaration $\sim switch\space I:=(D,D,...,D)$
	- Symbol classification declaration $\sim type(I,I,...,I)$
	- Comment declaration $\sim comment\space S_;$
	- Array declaration $\sim array(I,I,...,I[C:C\prime],I,...)$
	- Procedure declaration
		- $\sim procedure\space I(R) =: (R), I(R) =: (R), ..., I(R) =: (R)$
		- $\Delta;\Delta;...;\Delta;\space begin\space\Sigma;\Sigma;...;\Delta;\Delta;...;\Sigma;\Sigma\space end$