{"main": {"id": "5b54dc7a271424ac", "type": "split", "children": [{"id": "68063c9253c2c7bd", "type": "tabs", "children": [{"id": "b1d5c90ca28ae68b", "type": "leaf", "state": {"type": "markdown", "state": {"file": "algol_60.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "algol_60"}}]}], "direction": "vertical"}, "left": {"id": "6b1720eafa05b5ea", "type": "split", "children": [{"id": "090aeb29ccca218e", "type": "tabs", "children": [{"id": "0e859dd60c68a5b7", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "9fb5dcf39f48f375", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "fc6e371823313852", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "right": {"id": "5206bdeb561014f8", "type": "split", "children": [{"id": "61e1734f233026fb", "type": "tabs", "children": [{"id": "a4c2e6f6dbe1d6aa", "type": "leaf", "state": {"type": "backlink", "state": {"file": "Speedcoding.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for Speedcoding"}}, {"id": "b70096e8512bc068", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "Speedcoding.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from Speedcoding"}}, {"id": "27fb7de1df48fb36", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "9b67292381ba25e8", "type": "leaf", "state": {"type": "outline", "state": {"file": "Speedcoding.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of Speedcoding"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "b1d5c90ca28ae68b", "lastOpenFiles": ["GenTree.canvas", "algol_60.md", "fortrani.md", "algol_58.md", "apl.md", "snobol.md", "lisp.md", "cobol.md", "fortran.md", "speedcoding.md", "2025-07-15.md", "prog_lang_poster.pdf", "2025-07-13.md", "2025-07-07.md", "fortran/fortran90.md", "fortran/fortran2.md", "fortran/fortran1.md", "fortran/fortran.md", "Speedcoding.md"]}