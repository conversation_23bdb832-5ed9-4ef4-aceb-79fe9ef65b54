```BNF
<empty> ::= 
```
## Basic symbols, identifiers, numbers, and strings
### Letters
```BNF
<letter> ::= a|b|c|d|e|f|g|h|i|j|k|l|m|n|o|p|q|r|s|t|u|v|w|x|y|z|
    A|B|C|D|E|F|G|H|I|J|K|L|M|N|O|P|Q|R|S|T|U|V|W|X|Y|Z
```
### Digits
```BNF
<digit> ::= 0|1|2|3|4|5|6|7|8|9
```
### Logical values
```BNF
<logical value> ::= true|false
```
### Delimiters
```BNF
<delimiter> ::= <operator>|<separator>|<bracket>|<declarator>|<specificator>
<operator> ::= <arithmetic operator>|<relational operator>|<logical operator>|<sequential operator>
<arithmetic operator> ::= +|-|×|/|÷|↑
<relational operator> ::= <|≤|=|≥|>|>|≠
<logical operator> ::= ¬|∨|∧|≡|⊃
<sequential operator> ::= go to|if|then|else|for|do
<separator> ::= ,|.|;|:|:=|₁₀| |step|until|while|comment
<bracket> ::= (|)|[|]|'|'|begin|end
<declarator> ::= own|Boolean|integer|real|array|switch|procedure
<specificator> ::= string|label|value
```
### Identifiers
```BNF
<identifier> ::= <letter>|<identifier><letter>|<identifier><digit>
```
No semantic meaning
### Numbers
```BNF
<unsigned integer> ::= <digit>|<unsigned integer><digit>
<integer> ::= <unsigned integer>|+<unsigned integer>|-<unsigned integer>
<decimal fraction> ::= .<unsigned integer>
<exponent part> ::= ₁₀<integer>
<decimal number> ::= <unsigned integer>|<decimal fraction>|<unsigned integer><decimal fraction>
<unsigned number> ::= <decimal number>|<exponent part>|<decimal number><exponent part>
<number> ::= <unsigned number>|+<unsigned number>|-<unsigned number>
```
Semantic meaning: conventional
### Strings
```BNF
<proper string> ::= <any sequence of basic symbols not containing the symbol `'`>|<empty>
<open string> ::= <open string>|'<open string>'|<open string><open string>
<string> ::= '<open string>'
```
Semantic meaning: used as parameters of procedures
## Expressions
### Variables
```BNF
<variable identifier> ::= <identifier>
<simple variable> ::= <variable identifier>
<subscript expression> ::= <arithmetic expression>
<subscript list> ::= <subscript expression>|<subscript list>,<subscript expression>
<array identifier> ::= <identifier>
<subscripted variable> ::= <array identifier>[<subscript list>]
<variable> ::= <simple variable>|<subscripted variable>
```
Semantic meaning: a designation given to a single value
### Function designators
```BNF
<procedure identifier> ::= <identifier>
<actual parameter> ::= <string>|<expression>|<array identifier>|<switch identifier>|<procedure identifier>
<letter string> ::= <letter>|<letter string><letter>
<parameter delimiter> ::= ,|)<letter string> :(
<actual parameter list> ::= <actual parameter>|<actual parameter list><parameter delimiter><actual parameter>
<actual parameter part> ::= <empty>|(<actual parameter list>)
```